"""
财务软件专用PDF生成器
支持财务凭证、报表的专业PDF生成和A4横向打印
"""

from flask import current_app
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm, mm
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.graphics.shapes import Drawing, Line
from reportlab.graphics.charts.linecharts import HorizontalLineChart
from reportlab.graphics.charts.barcharts import VerticalBarChart
import os
from datetime import datetime, date
from decimal import Decimal
import pandas as pd
from typing import List, Dict, Any, Optional

class FinancialPDFGenerator:
    """财务PDF生成器"""
    
    def __init__(self):
        self.register_fonts()
        self.setup_styles()
    
    def register_fonts(self):
        """注册中文字体"""
        try:
            font_path = os.path.join(current_app.root_path, 'static', 'fonts', 'simsun.ttf')
            if os.path.exists(font_path):
                pdfmetrics.registerFont(TTFont('SimSun', font_path))
                pdfmetrics.registerFont(TTFont('SimSun-Bold', font_path))
                current_app.logger.info("Successfully registered SimSun font")
            else:
                current_app.logger.warning("SimSun font not found, using default font")
        except Exception as e:
            current_app.logger.error(f"Font registration failed: {str(e)}")
    
    def setup_styles(self):
        """设置样式"""
        self.styles = {
            'title': ParagraphStyle(
                'FinancialTitle',
                fontName='SimSun-Bold',
                fontSize=18,
                leading=22,
                alignment=1,  # 居中
                spaceAfter=12
            ),
            'subtitle': ParagraphStyle(
                'FinancialSubtitle',
                fontName='SimSun',
                fontSize=14,
                leading=18,
                alignment=1,
                spaceAfter=8
            ),
            'normal': ParagraphStyle(
                'FinancialNormal',
                fontName='SimSun',
                fontSize=10,
                leading=12,
                alignment=0  # 左对齐
            ),
            'small': ParagraphStyle(
                'FinancialSmall',
                fontName='SimSun',
                fontSize=8,
                leading=10,
                alignment=0
            ),
            'right': ParagraphStyle(
                'FinancialRight',
                fontName='SimSun',
                fontSize=10,
                leading=12,
                alignment=2  # 右对齐
            )
        }
    
    def create_pdf_dir(self, subdir='financial'):
        """创建PDF保存目录"""
        try:
            pdf_dir = os.path.join(current_app.root_path, 'static', 'pdf', subdir)
            if not os.path.exists(pdf_dir):
                os.makedirs(pdf_dir)
            return pdf_dir
        except Exception as e:
            current_app.logger.error(f"Failed to create PDF directory: {str(e)}")
            raise
    
    def generate_voucher_pdf(self, voucher_id: int, landscape_mode: bool = True) -> str:
        """
        生成财务凭证PDF - A4横向格式
        
        Args:
            voucher_id: 凭证ID
            landscape_mode: 是否横向打印
            
        Returns:
            PDF文件相对路径
        """
        from app.models_financial import FinancialVoucher, VoucherDetail
        
        # 获取凭证信息
        voucher = FinancialVoucher.query.get_or_404(voucher_id)
        details = VoucherDetail.query.filter_by(voucher_id=voucher_id).all()
        
        # 创建PDF保存目录
        pdf_dir = self.create_pdf_dir('vouchers')
        
        # 生成PDF文件名
        filename = f"财务凭证_{voucher.voucher_number}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf"
        pdf_path = os.path.join(pdf_dir, filename)
        
        # 设置页面大小
        pagesize = landscape(A4) if landscape_mode else A4
        
        # 创建PDF文档
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=pagesize,
            rightMargin=15*mm,
            leftMargin=15*mm,
            topMargin=15*mm,
            bottomMargin=15*mm
        )
        
        content = []
        
        # 添加标题
        title = Paragraph("记账凭证", self.styles['title'])
        content.append(title)
        content.append(Spacer(1, 10*mm))
        
        # 凭证基本信息
        voucher_info = [
            [
                Paragraph("凭证字号:", self.styles['normal']),
                Paragraph(f"{voucher.voucher_type} 第{voucher.voucher_number}号", self.styles['normal']),
                Paragraph("日期:", self.styles['normal']),
                Paragraph(voucher.voucher_date.strftime('%Y年%m月%d日'), self.styles['normal'])
            ]
        ]
        
        info_table = Table(voucher_info, colWidths=[25*mm, 60*mm, 20*mm, 40*mm])
        info_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'SimSun'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LINEBELOW', (1, 0), (1, 0), 1, colors.black),
            ('LINEBELOW', (3, 0), (3, 0), 1, colors.black),
        ]))
        
        content.append(info_table)
        content.append(Spacer(1, 10*mm))
        
        # 凭证明细表头
        headers = ["摘要", "会计科目", "借方金额", "贷方金额"]
        
        # 凭证明细数据
        detail_data = [headers]
        total_debit = Decimal('0')
        total_credit = Decimal('0')
        
        for detail in details:
            detail_data.append([
                detail.summary or voucher.summary,
                f"{detail.subject.code} {detail.subject.name}",
                f"{detail.debit_amount:.2f}" if detail.debit_amount > 0 else "",
                f"{detail.credit_amount:.2f}" if detail.credit_amount > 0 else ""
            ])
            total_debit += detail.debit_amount or Decimal('0')
            total_credit += detail.credit_amount or Decimal('0')
        
        # 添加合计行
        detail_data.append([
            "合计", "", f"{total_debit:.2f}", f"{total_credit:.2f}"
        ])
        
        # 创建明细表格
        detail_table = Table(detail_data, colWidths=[60*mm, 80*mm, 40*mm, 40*mm])
        detail_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'SimSun'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('ALIGN', (2, 1), (-1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTSIZE', (0, -1), (-1, -1), 12),
            ('FONTNAME', (0, -1), (-1, -1), 'SimSun-Bold'),
        ]))
        
        content.append(detail_table)
        content.append(Spacer(1, 15*mm))
        
        # 签名栏
        signature_data = [
            [
                Paragraph("制单:", self.styles['normal']),
                "",
                Paragraph("审核:", self.styles['normal']),
                "",
                Paragraph("记账:", self.styles['normal']),
                "",
                Paragraph("出纳:", self.styles['normal']),
                ""
            ]
        ]
        
        signature_table = Table(signature_data, colWidths=[20*mm, 30*mm, 20*mm, 30*mm, 20*mm, 30*mm, 20*mm, 30*mm])
        signature_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'SimSun'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LINEBELOW', (1, 0), (1, 0), 0.5, colors.black),
            ('LINEBELOW', (3, 0), (3, 0), 0.5, colors.black),
            ('LINEBELOW', (5, 0), (5, 0), 0.5, colors.black),
            ('LINEBELOW', (7, 0), (7, 0), 0.5, colors.black),
        ]))
        
        content.append(signature_table)
        
        # 生成PDF
        doc.build(content)
        
        # 返回相对路径
        return os.path.join('pdf', 'vouchers', filename)

    def generate_voucher_pdf_enhanced(self, voucher, details, user_area, landscape_mode=True):
        """
        生成增强版财务凭证PDF - 与打印模块保持一致

        Args:
            voucher: 凭证对象
            details: 过滤后的凭证明细列表
            user_area: 用户区域对象
            landscape_mode: 是否横向模式

        Returns:
            PDF文件相对路径
        """
        # 创建PDF保存目录
        pdf_dir = self.create_pdf_dir('vouchers')

        # 生成PDF文件名
        filename = f"财务凭证_{voucher.voucher_number}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf"
        pdf_path = os.path.join(pdf_dir, filename)

        # 设置页面大小
        pagesize = landscape(A4) if landscape_mode else A4

        # 创建PDF文档
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=pagesize,
            rightMargin=15*mm,
            leftMargin=15*mm,
            topMargin=15*mm,
            bottomMargin=15*mm
        )

        content = []

        # 标题
        title = Paragraph("记账凭证", self.styles['title'])
        content.append(title)
        content.append(Spacer(1, 5*mm))

        # 公司名称
        company_name = Paragraph(user_area.name, self.styles['subtitle'])
        content.append(company_name)
        content.append(Spacer(1, 8*mm))

        # 凭证基本信息（与打印模块保持一致）
        voucher_info = [
            [
                Paragraph("凭证字:", self.styles['normal']),
                Paragraph(voucher.voucher_type, self.styles['normal']),
                Paragraph("号:", self.styles['normal']),
                Paragraph(voucher.voucher_number, self.styles['normal']),
                Paragraph("日期:", self.styles['normal']),
                Paragraph(voucher.voucher_date.strftime('%Y-%m-%d'), self.styles['normal']),
                Paragraph("附件:", self.styles['normal']),
                Paragraph(f"{voucher.attachment_count or 0}张", self.styles['normal'])
            ]
        ]

        info_table = Table(voucher_info, colWidths=[20*mm, 25*mm, 15*mm, 25*mm, 20*mm, 25*mm, 20*mm, 20*mm])
        info_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'SimSun'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ]))

        content.append(info_table)
        content.append(Spacer(1, 8*mm))

        # 凭证明细表头
        headers = ["序号", "摘要", "会计科目", "借方金额", "贷方金额"]

        # 凭证明细数据
        detail_data = [headers]
        total_debit = Decimal('0')
        total_credit = Decimal('0')

        for i, detail in enumerate(details, 1):
            # 格式化金额（与打印模块保持一致）
            debit_amount = f"{detail.debit_amount:,.2f}元" if detail.debit_amount > 0 else ""
            credit_amount = f"{detail.credit_amount:,.2f}元" if detail.credit_amount > 0 else ""

            detail_data.append([
                str(i),
                detail.summary or voucher.summary or '',
                f"{detail.subject.code} {detail.subject.name}",
                debit_amount,
                credit_amount
            ])
            total_debit += detail.debit_amount or Decimal('0')
            total_credit += detail.credit_amount or Decimal('0')

        # 添加合计行（与打印模块保持一致）
        detail_data.append([
            "合计", "", "",
            f"{total_debit:,.2f}元",
            f"{total_credit:,.2f}元"
        ])

        # 创建明细表格
        detail_table = Table(detail_data, colWidths=[20*mm, 60*mm, 60*mm, 35*mm, 35*mm])
        detail_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'SimSun'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),
            ('ALIGN', (0, 0), (0, -1), 'CENTER'),  # 序号居中
            ('ALIGN', (3, 1), (-1, -1), 'RIGHT'),  # 金额右对齐
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTSIZE', (0, -1), (-1, -1), 12),
            ('FONTNAME', (0, -1), (-1, -1), 'SimSun-Bold'),
        ]))

        content.append(detail_table)
        content.append(Spacer(1, 10*mm))

        # 大写金额（与打印模块保持一致）
        def convert_amount_to_chinese(amount):
            """转换金额为中文大写"""
            if amount == 0:
                return "零元整"

            digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
            units = ['', '拾', '佰', '仟']
            big_units = ['', '万', '亿']

            amount_str = str(int(amount * 100))
            result = ''

            # 处理分
            if len(amount_str) >= 1:
                fen = int(amount_str[-1])
                if fen > 0:
                    result = digits[fen] + '分' + result

            # 处理角
            if len(amount_str) >= 2:
                jiao = int(amount_str[-2])
                if jiao > 0:
                    result = digits[jiao] + '角' + result
                elif result and len(amount_str) > 2:
                    result = '零' + result

            # 处理元及以上
            if len(amount_str) > 2:
                yuan_str = amount_str[:-2]
                if yuan_str == '0':
                    result = '零元' + result
                else:
                    # 简化处理，这里可以进一步完善
                    yuan_amount = int(yuan_str)
                    if yuan_amount > 0:
                        result = f"{digits[yuan_amount % 10]}元" + result if yuan_amount < 10 else f"数字转换元" + result
            else:
                result = '零元' + result

            return f"人民币{result}整" if result else "人民币零元整"

        chinese_amount = convert_amount_to_chinese(float(total_debit))
        amount_info = [
            [Paragraph(f"金额大写：{chinese_amount}", self.styles['normal'])]
        ]

        amount_table = Table(amount_info, colWidths=[200*mm])
        amount_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'SimSun'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ]))

        content.append(amount_table)
        content.append(Spacer(1, 10*mm))

        # 签名栏（与打印模块保持一致）
        signature_data = [
            [
                Paragraph("制单:", self.styles['normal']),
                Paragraph(voucher.creator.username if voucher.creator else "", self.styles['normal']),
                Paragraph("审核:", self.styles['normal']),
                Paragraph(voucher.reviewer.username if voucher.reviewer else "", self.styles['normal']),
                Paragraph("记账:", self.styles['normal']),
                Paragraph(voucher.poster.username if voucher.poster else "", self.styles['normal']),
                Paragraph("出纳:", self.styles['normal']),
                Paragraph("", self.styles['normal'])
            ]
        ]

        signature_table = Table(signature_data, colWidths=[20*mm, 30*mm, 20*mm, 30*mm, 20*mm, 30*mm, 20*mm, 30*mm])
        signature_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'SimSun'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ]))

        content.append(signature_table)

        # 生成PDF
        doc.build(content)

        # 返回相对路径
        return os.path.join('pdf', 'vouchers', filename)

    def generate_balance_sheet_pdf(self, balance_data: Dict[str, Any], balance_date: date) -> str:
        """
        生成资产负债表PDF

        Args:
            balance_data: 资产负债表数据
            balance_date: 报表日期

        Returns:
            PDF文件相对路径
        """
        # 创建PDF保存目录
        pdf_dir = self.create_pdf_dir('reports')

        # 生成PDF文件名
        filename = f"资产负债表_{balance_date.strftime('%Y%m%d')}_{datetime.now().strftime('%H%M%S')}.pdf"
        pdf_path = os.path.join(pdf_dir, filename)

        # 创建PDF文档 - A4纵向
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=A4,
            rightMargin=20*mm,
            leftMargin=20*mm,
            topMargin=20*mm,
            bottomMargin=20*mm
        )

        content = []

        # 添加标题
        title = Paragraph("资产负债表", self.styles['title'])
        content.append(title)

        # 添加报表日期
        date_info = Paragraph(f"报表日期：{balance_date.strftime('%Y年%m月%d日')}", self.styles['subtitle'])
        content.append(date_info)
        content.append(Spacer(1, 10*mm))

        # 资产负债表数据
        table_data = [
            ["资产", "金额", "负债和所有者权益", "金额"]
        ]

        # 处理资产数据
        assets = balance_data.get('assets', {})
        liabilities = balance_data.get('liabilities', {})
        equity = balance_data.get('equity', {})

        max_rows = max(
            len(assets.get('items', [])),
            len(liabilities.get('items', [])) + len(equity.get('items', []))
        )

        # 填充表格数据
        for i in range(max_rows):
            row = ["", "", "", ""]

            # 资产项目
            if i < len(assets.get('items', [])):
                asset_item = assets['items'][i]
                row[0] = asset_item.get('name', '')
                row[1] = f"{asset_item.get('amount', 0):.2f}"

            # 负债和所有者权益项目
            liability_items = liabilities.get('items', [])
            equity_items = equity.get('items', [])
            all_right_items = liability_items + equity_items

            if i < len(all_right_items):
                right_item = all_right_items[i]
                row[2] = right_item.get('name', '')
                row[3] = f"{right_item.get('amount', 0):.2f}"

            table_data.append(row)

        # 添加合计行
        table_data.append([
            "资产总计",
            f"{assets.get('total_assets', 0):.2f}",
            "负债和所有者权益总计",
            f"{liabilities.get('total_liabilities', 0) + equity.get('total_equity', 0):.2f}"
        ])

        # 创建表格
        balance_table = Table(table_data, colWidths=[70*mm, 30*mm, 70*mm, 30*mm])
        balance_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'SimSun'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('ALIGN', (1, 1), (1, -1), 'RIGHT'),
            ('ALIGN', (3, 1), (3, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTNAME', (0, -1), (-1, -1), 'SimSun-Bold'),
        ]))

        content.append(balance_table)
        content.append(Spacer(1, 10*mm))

        # 添加平衡检查
        balance_check = balance_data.get('balance_check', True)
        check_text = "✓ 平衡检查：通过" if balance_check else "✗ 平衡检查：不平衡"
        check_para = Paragraph(check_text, self.styles['normal'])
        content.append(check_para)

        # 生成PDF
        doc.build(content)

        # 返回相对路径
        return os.path.join('pdf', 'reports', filename)


class FinancialExcelExporter:
    """财务Excel导出器"""

    def __init__(self):
        self.setup_styles()

    def setup_styles(self):
        """设置Excel样式"""
        try:
            from openpyxl.styles import Font, Alignment, Border, Side, PatternFill

            self.styles = {
                'title': {
                    'font': Font(name='宋体', size=16, bold=True),
                    'alignment': Alignment(horizontal='center', vertical='center')
                },
                'header': {
                    'font': Font(name='宋体', size=12, bold=True),
                    'alignment': Alignment(horizontal='center', vertical='center'),
                    'fill': PatternFill(start_color='D9D9D9', end_color='D9D9D9', fill_type='solid'),
                    'border': Border(
                        left=Side(style='thin'),
                        right=Side(style='thin'),
                        top=Side(style='thin'),
                        bottom=Side(style='thin')
                    )
                },
                'data': {
                    'font': Font(name='宋体', size=10),
                    'alignment': Alignment(horizontal='left', vertical='center'),
                    'border': Border(
                        left=Side(style='thin'),
                        right=Side(style='thin'),
                        top=Side(style='thin'),
                        bottom=Side(style='thin')
                    )
                },
                'amount': {
                    'font': Font(name='宋体', size=10),
                    'alignment': Alignment(horizontal='right', vertical='center'),
                    'border': Border(
                        left=Side(style='thin'),
                        right=Side(style='thin'),
                        top=Side(style='thin'),
                        bottom=Side(style='thin')
                    ),
                    'number_format': '#,##0.00'
                }
            }
        except ImportError:
            current_app.logger.warning("openpyxl not available, Excel export will use basic formatting")
            self.styles = {}

    def create_excel_dir(self, subdir='financial'):
        """创建Excel保存目录"""
        try:
            excel_dir = os.path.join(current_app.root_path, 'static', 'excel', subdir)
            if not os.path.exists(excel_dir):
                os.makedirs(excel_dir)
            return excel_dir
        except Exception as e:
            current_app.logger.error(f"Failed to create Excel directory: {str(e)}")
            raise

    def export_voucher_to_excel(self, voucher_id: int) -> str:
        """
        导出财务凭证到Excel

        Args:
            voucher_id: 凭证ID

        Returns:
            Excel文件相对路径
        """
        from app.models_financial import FinancialVoucher, VoucherDetail

        # 获取凭证信息
        voucher = FinancialVoucher.query.get_or_404(voucher_id)
        details = VoucherDetail.query.filter_by(voucher_id=voucher_id).all()

        # 创建Excel保存目录
        excel_dir = self.create_excel_dir('vouchers')

        # 生成Excel文件名
        filename = f"财务凭证_{voucher.voucher_number}_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
        excel_path = os.path.join(excel_dir, filename)

        # 创建DataFrame
        data = []
        for detail in details:
            data.append({
                '凭证号': voucher.voucher_number,
                '日期': voucher.voucher_date.strftime('%Y-%m-%d'),
                '摘要': detail.summary or voucher.summary,
                '科目编码': detail.subject.code,
                '科目名称': detail.subject.name,
                '借方金额': float(detail.debit_amount) if detail.debit_amount > 0 else None,
                '贷方金额': float(detail.credit_amount) if detail.credit_amount > 0 else None
            })

        df = pd.DataFrame(data)

        # 导出到Excel
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='财务凭证', index=False)

            # 应用样式
            if self.styles:
                workbook = writer.book
                worksheet = writer.sheets['财务凭证']

                # 设置列宽
                worksheet.column_dimensions['A'].width = 15
                worksheet.column_dimensions['B'].width = 12
                worksheet.column_dimensions['C'].width = 30
                worksheet.column_dimensions['D'].width = 12
                worksheet.column_dimensions['E'].width = 25
                worksheet.column_dimensions['F'].width = 15
                worksheet.column_dimensions['G'].width = 15

                # 应用表头样式
                for col in range(1, 8):
                    cell = worksheet.cell(row=1, column=col)
                    cell.font = self.styles['header']['font']
                    cell.alignment = self.styles['header']['alignment']
                    cell.fill = self.styles['header']['fill']
                    cell.border = self.styles['header']['border']

        # 返回相对路径
        return os.path.join('excel', 'vouchers', filename)

    def export_balance_sheet_to_excel(self, balance_data: Dict[str, Any], balance_date: date) -> str:
        """
        导出资产负债表到Excel

        Args:
            balance_data: 资产负债表数据
            balance_date: 报表日期

        Returns:
            Excel文件相对路径
        """
        # 创建Excel保存目录
        excel_dir = self.create_excel_dir('reports')

        # 生成Excel文件名
        filename = f"资产负债表_{balance_date.strftime('%Y%m%d')}_{datetime.now().strftime('%H%M%S')}.xlsx"
        excel_path = os.path.join(excel_dir, filename)

        # 准备数据
        assets = balance_data.get('assets', {})
        liabilities = balance_data.get('liabilities', {})
        equity = balance_data.get('equity', {})

        # 创建资产数据
        asset_data = []
        for item in assets.get('items', []):
            asset_data.append({
                '项目': item.get('name', ''),
                '金额': float(item.get('amount', 0))
            })
        asset_data.append({'项目': '资产总计', '金额': float(assets.get('total_assets', 0))})

        # 创建负债数据
        liability_data = []
        for item in liabilities.get('items', []):
            liability_data.append({
                '项目': item.get('name', ''),
                '金额': float(item.get('amount', 0))
            })
        liability_data.append({'项目': '负债总计', '金额': float(liabilities.get('total_liabilities', 0))})

        # 创建所有者权益数据
        equity_data = []
        for item in equity.get('items', []):
            equity_data.append({
                '项目': item.get('name', ''),
                '金额': float(item.get('amount', 0))
            })
        equity_data.append({'项目': '所有者权益总计', '金额': float(equity.get('total_equity', 0))})

        # 导出到Excel
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # 资产表
            pd.DataFrame(asset_data).to_excel(writer, sheet_name='资产', index=False)

            # 负债表
            pd.DataFrame(liability_data).to_excel(writer, sheet_name='负债', index=False)

            # 所有者权益表
            pd.DataFrame(equity_data).to_excel(writer, sheet_name='所有者权益', index=False)

            # 应用样式
            if self.styles:
                workbook = writer.book

                for sheet_name in ['资产', '负债', '所有者权益']:
                    worksheet = writer.sheets[sheet_name]

                    # 设置列宽
                    worksheet.column_dimensions['A'].width = 30
                    worksheet.column_dimensions['B'].width = 20

                    # 应用表头样式
                    for col in range(1, 3):
                        cell = worksheet.cell(row=1, column=col)
                        cell.font = self.styles['header']['font']
                        cell.alignment = self.styles['header']['alignment']
                        cell.fill = self.styles['header']['fill']
                        cell.border = self.styles['header']['border']

        # 返回相对路径
        return os.path.join('excel', 'reports', filename)

    def export_trial_balance_to_excel(self, trial_balance_data: List[Dict[str, Any]],
                                    start_date: date, end_date: date) -> str:
        """
        导出试算平衡表到Excel

        Args:
            trial_balance_data: 试算平衡表数据
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            Excel文件相对路径
        """
        # 创建Excel保存目录
        excel_dir = self.create_excel_dir('reports')

        # 生成Excel文件名
        filename = f"试算平衡表_{start_date.strftime('%Y%m%d')}至{end_date.strftime('%Y%m%d')}_{datetime.now().strftime('%H%M%S')}.xlsx"
        excel_path = os.path.join(excel_dir, filename)

        # 创建DataFrame
        df = pd.DataFrame(trial_balance_data)

        # 导出到Excel
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='试算平衡表', index=False)

            # 应用样式
            if self.styles:
                workbook = writer.book
                worksheet = writer.sheets['试算平衡表']

                # 设置列宽
                worksheet.column_dimensions['A'].width = 15  # 科目编码
                worksheet.column_dimensions['B'].width = 25  # 科目名称
                worksheet.column_dimensions['C'].width = 15  # 科目类型
                worksheet.column_dimensions['D'].width = 15  # 期初余额
                worksheet.column_dimensions['E'].width = 15  # 借方发生额
                worksheet.column_dimensions['F'].width = 15  # 贷方发生额
                worksheet.column_dimensions['G'].width = 15  # 期末余额

                # 应用表头样式
                for col in range(1, 8):
                    cell = worksheet.cell(row=1, column=col)
                    cell.font = self.styles['header']['font']
                    cell.alignment = self.styles['header']['alignment']
                    cell.fill = self.styles['header']['fill']
                    cell.border = self.styles['header']['border']

        # 返回相对路径
        return os.path.join('excel', 'reports', filename)


# 创建全局实例
financial_pdf_generator = FinancialPDFGenerator()
financial_excel_exporter = FinancialExcelExporter()
