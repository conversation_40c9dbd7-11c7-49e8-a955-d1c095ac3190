2025-06-21 16:55:14,718 INFO: 应用启动 - PID: 9032 [in C:\StudentsCMSSP\app\__init__.py:862]
2025-06-21 16:55:57,377 INFO: 食材分类映射: 其他 -> 1201(原材料), 金额: 1948.4, 明细: 面粉(100公斤)、大米(108.3公斤)、面条(47.0公斤) [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:1800]
2025-06-21 16:55:57,377 INFO: 食材分类映射: 肉类 -> 120102(肉类), 金额: 8300.0, 明细: 猪肉(100公斤)、鸡蛋(100公斤) [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:1800]
2025-06-21 16:55:57,450 INFO: 财务凭证生成成功: 凭证号=1, 入库单=RK20250614230014, 供应商=绿色农场有限公司, 总金额=10248.40 [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:1853]
2025-06-21 16:57:09,206 INFO: Successfully registered SimSun font [in C:\StudentsCMSSP\app\utils\financial_pdf_generator.py:37]
2025-06-21 16:57:09,439 ERROR: 导出凭证PDF失败: 'int' object has no attribute 'username' [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:2287]
2025-06-21 16:57:09,441 ERROR: Exception on /financial/vouchers/54/export/pdf [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\app\routes\financial\vouchers.py", line 2275, in export_voucher_pdf
    pdf_path = financial_pdf_generator.generate_voucher_pdf_enhanced(
  File "C:\StudentsCMSSP\app\utils\financial_pdf_generator.py", line 416, in generate_voucher_pdf_enhanced
    Paragraph(voucher.created_by.username if voucher.created_by else "", self.styles['normal']),
AttributeError: 'int' object has no attribute 'username'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\vouchers.py", line 2289, in export_voucher_pdf
    return redirect(url_for('financial.view_voucher', voucher_id=voucher_id))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\helpers.py", line 220, in url_for
    return current_app.url_for(
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1084, in url_for
    return self.handle_url_build_error(error, endpoint, values)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1073, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'financial.view_voucher' with values ['voucher_id']. Did you forget to specify values ['id']?
2025-06-21 17:00:05,352 INFO: 成功生成二维码，数据: http://xiaoyuanst.com/food-trace/recipe/401/2025-06-21/%E5%8D%88%E9%A4%90/44... [in C:\StudentsCMSSP\app\routes\stock_in.py:2986]
2025-06-21 17:01:45,579 ERROR: 导出凭证PDF失败: 'int' object has no attribute 'username' [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:2287]
2025-06-21 17:01:45,579 ERROR: Exception on /financial/vouchers/54/export/pdf [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\app\routes\financial\vouchers.py", line 2275, in export_voucher_pdf
    pdf_path = financial_pdf_generator.generate_voucher_pdf_enhanced(
  File "C:\StudentsCMSSP\app\utils\financial_pdf_generator.py", line 416, in generate_voucher_pdf_enhanced
    Paragraph(voucher.creator.username if voucher.creator else "", self.styles['normal']),
AttributeError: 'int' object has no attribute 'username'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\vouchers.py", line 2289, in export_voucher_pdf
    return redirect(url_for('financial.view_voucher', voucher_id=voucher_id))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\helpers.py", line 220, in url_for
    return current_app.url_for(
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1084, in url_for
    return self.handle_url_build_error(error, endpoint, values)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1073, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'financial.view_voucher' with values ['voucher_id']. Did you forget to specify values ['id']?
