{% extends "financial/base.html" %}

{% block title %}总账查询{% endblock %}

{% block page_title %}总账查询{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务报表</a></span>
<span class="uf-breadcrumb-item active">总账查询</span>
{% endblock %}

{% block page_actions %}
<div class="uf-toolbar-group">
    <button type="button" class="uf-btn uf-btn-primary" onclick="refreshGeneralLedger()">
        <i class="fas fa-sync-alt uf-icon"></i> 刷新
    </button>
    <button type="button" class="uf-btn uf-btn-success" onclick="generateAllLedgers()">
        <i class="fas fa-cogs uf-icon"></i> 批量生成
    </button>
    <div class="uf-toolbar-separator"></div>
    <button type="button" class="uf-btn uf-btn-info" onclick="exportGeneralLedger()">
        <i class="fas fa-file-excel uf-icon"></i> 导出Excel
    </button>
    <button type="button" class="uf-btn uf-btn-secondary" onclick="printGeneralLedger()">
        <i class="fas fa-print uf-icon"></i> 打印报表
    </button>
    <div class="uf-toolbar-separator"></div>
    <button type="button" class="uf-btn uf-btn-warning" onclick="showBalanceAnalysis()">
        <i class="fas fa-chart-bar uf-icon"></i> 分析
    </button>
</div>
{% endblock %}

{% block financial_content %}
<!-- 用友风格总账查询窗口 -->
<div class="uf-general-ledger-container">
    <!-- 查询条件和操作按钮一行显示 -->
    <div class="uf-card uf-query-card">
        <div class="uf-card-body" style="padding: 12px 16px;">
            <form method="GET" class="uf-query-form">
                <div class="uf-query-actions-row">
                    <!-- 查询条件组 -->
                    <div class="uf-query-conditions">
                        <div class="uf-form-group">
                            <label class="uf-form-label">开始日期：</label>
                            <input type="date" class="uf-form-control" id="start_date" name="start_date"
                                   value="{{ start_date }}" required>
                        </div>
                        <div class="uf-form-group">
                            <label class="uf-form-label">结束日期：</label>
                            <input type="date" class="uf-form-control" id="end_date" name="end_date"
                                   value="{{ end_date }}" required>
                        </div>
                        <div class="uf-form-group">
                            <label class="uf-form-label">科目类型：</label>
                            <select class="uf-form-control" id="subject_type" name="subject_type">
                                <option value="">全部类型</option>
                                <option value="资产" {% if subject_type == '资产' %}selected{% endif %}>资产</option>
                                <option value="负债" {% if subject_type == '负债' %}selected{% endif %}>负债</option>
                                <option value="所有者权益" {% if subject_type == '所有者权益' %}selected{% endif %}>所有者权益</option>
                                <option value="收入" {% if subject_type == '收入' %}selected{% endif %}>收入</option>
                                <option value="费用" {% if subject_type == '费用' %}selected{% endif %}>费用</option>
                            </select>
                        </div>
                        <div class="uf-form-group">
                            <label class="uf-form-label">显示零余额：</label>
                            <select class="uf-form-control" id="show_zero" name="show_zero">
                                <option value="0">隐藏</option>
                                <option value="1" {% if show_zero %}selected{% endif %}>显示</option>
                            </select>
                        </div>
                    </div>

                    <!-- 操作按钮组 -->
                    <div class="uf-query-actions">
                        <button type="submit" class="uf-btn uf-btn-primary">
                            <i class="fas fa-search uf-icon"></i> 查询
                        </button>
                        <button type="button" class="uf-btn uf-btn-secondary" onclick="resetQueryForm()">
                            <i class="fas fa-undo uf-icon"></i> 重置
                        </button>
                        <button type="button" class="uf-btn uf-btn-warning" onclick="quickQuery('month')">
                            <i class="fas fa-calendar uf-icon"></i> 本月
                        </button>
                        <button type="button" class="uf-btn uf-btn-warning" onclick="quickQuery('year')">
                            <i class="fas fa-calendar-alt uf-icon"></i> 本年
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 总账汇总表 -->
    <div class="uf-card uf-ledger-summary-card">
        <div class="uf-card-header">
            <div class="uf-card-header-title">
                <i class="fas fa-table uf-card-header-icon"></i>
                总账汇总表
            </div>
            <div class="uf-card-header-actions">
                <span class="uf-record-count">
                    {% if general_ledger_data %}
                        共 {{ general_ledger_data|length }} 个科目
                    {% else %}
                        无数据
                    {% endif %}
                </span>
                <div class="uf-header-tools">
                    <button type="button" class="uf-btn uf-btn-sm uf-btn-outline" onclick="toggleTableView('summary')" title="全屏">
                        <i class="fas fa-expand-arrows-alt uf-icon"></i>
                    </button>
                    <button type="button" class="uf-btn uf-btn-sm uf-btn-outline" onclick="refreshSummaryTable()" title="刷新">
                        <i class="fas fa-sync-alt uf-icon"></i>
                    </button>
                    <button type="button" class="uf-btn uf-btn-sm uf-btn-outline" onclick="showTableSettings()" title="设置">
                        <i class="fas fa-cog uf-icon"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="uf-card-body" style="padding: 0;">
            <!-- 表格工具栏 -->
            <div class="uf-table-toolbar">
                <div class="uf-table-filters">
                    <input type="text" class="uf-filter-input" id="subjectFilter" placeholder="搜索科目..." onkeyup="filterTable()">
                    <select class="uf-filter-select" id="typeFilter" onchange="filterTable()">
                        <option value="">全部类型</option>
                        <option value="资产">资产</option>
                        <option value="负债">负债</option>
                        <option value="所有者权益">所有者权益</option>
                        <option value="收入">收入</option>
                        <option value="费用">费用</option>
                    </select>
                </div>
                <div class="uf-table-actions">
                    <button type="button" class="uf-btn uf-btn-xs uf-btn-outline" onclick="selectAllRows()">
                        <i class="fas fa-check-square uf-icon"></i> 全选
                    </button>
                    <button type="button" class="uf-btn uf-btn-xs uf-btn-outline" onclick="clearSelection()">
                        <i class="fas fa-square uf-icon"></i> 清除
                    </button>
                    <button type="button" class="uf-btn uf-btn-xs uf-btn-success" onclick="batchExportSelected()">
                        <i class="fas fa-download uf-icon"></i> 导出选中
                    </button>
                </div>
            </div>

            <div class="uf-table-container" id="summaryTableContainer">
                <table class="uf-table uf-general-ledger-table" id="generalLedgerTable">
                    <thead>
                        <tr>
                            <th class="uf-col-checkbox">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th class="uf-col-code sortable" onclick="sortTable(1)">
                                科目编码 <i class="fas fa-sort uf-sort-icon"></i>
                            </th>
                            <th class="uf-col-name sortable" onclick="sortTable(2)">
                                科目名称 <i class="fas fa-sort uf-sort-icon"></i>
                            </th>
                            <th class="uf-col-type sortable" onclick="sortTable(3)">
                                科目类型 <i class="fas fa-sort uf-sort-icon"></i>
                            </th>
                            <th class="uf-col-direction">余额方向</th>
                            <th class="uf-col-opening sortable" onclick="sortTable(5)">
                                期初余额 <i class="fas fa-sort uf-sort-icon"></i>
                            </th>
                            <th class="uf-col-debit sortable" onclick="sortTable(6)">
                                本期借方 <i class="fas fa-sort uf-sort-icon"></i>
                            </th>
                            <th class="uf-col-credit sortable" onclick="sortTable(7)">
                                本期贷方 <i class="fas fa-sort uf-sort-icon"></i>
                            </th>
                            <th class="uf-col-ending sortable" onclick="sortTable(8)">
                                期末余额 <i class="fas fa-sort uf-sort-icon"></i>
                            </th>
                            <th class="uf-col-operations">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% set total_opening = 0 %}
                        {% set total_debit = 0 %}
                        {% set total_credit = 0 %}
                        {% set total_ending = 0 %}

                        {% for item in general_ledger_data %}
                        {% set total_opening = total_opening + item.opening_balance %}
                        {% set total_debit = total_debit + item.period_debit %}
                        {% set total_credit = total_credit + item.period_credit %}
                        {% set total_ending = total_ending + item.ending_balance %}

                        <tr class="uf-ledger-row" data-subject-id="{{ item.id }}" data-subject-type="{{ item.subject_type }}">
                            <td class="uf-checkbox-cell">
                                <input type="checkbox" class="uf-row-checkbox" value="{{ item.id }}">
                            </td>
                            <td class="uf-subject-code">
                                <span class="uf-code-text">{{ item.code }}</span>
                            </td>
                            <td class="uf-subject-name">
                                <div class="uf-name-content">
                                    <span class="uf-name-text">{{ item.name }}</span>
                                    {% if item.description %}
                                    <small class="uf-name-desc">{{ item.description }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="uf-type-cell">
                                <span class="uf-status uf-status-{{ item.subject_type|lower }}">{{ item.subject_type }}</span>
                            </td>
                            <td class="uf-direction-cell">
                                <span class="uf-direction {% if item.balance_direction == '借方' %}uf-direction-debit{% else %}uf-direction-credit{% endif %}">
                                    {{ item.balance_direction }}
                                </span>
                            </td>
                            <td class="uf-amount-cell uf-opening-cell">
                                {% if item.opening_balance != 0 %}
                                    <span class="uf-amount {% if item.opening_balance > 0 %}uf-positive{% else %}uf-negative{% endif %}">
                                        {{ "{:,.2f}"|format(item.opening_balance|float) }}
                                    </span>
                                {% else %}
                                    <span class="uf-amount-zero">-</span>
                                {% endif %}
                            </td>
                            <td class="uf-amount-cell uf-debit-cell">
                                {% if item.period_debit > 0 %}
                                    <span class="uf-amount uf-debit-amount">{{ "{:,.2f}"|format(item.period_debit|float) }}</span>
                                {% else %}
                                    <span class="uf-amount-zero">-</span>
                                {% endif %}
                            </td>
                            <td class="uf-amount-cell uf-credit-cell">
                                {% if item.period_credit > 0 %}
                                    <span class="uf-amount uf-credit-amount">{{ "{:,.2f}"|format(item.period_credit|float) }}</span>
                                {% else %}
                                    <span class="uf-amount-zero">-</span>
                                {% endif %}
                            </td>
                            <td class="uf-amount-cell uf-ending-cell">
                                {% if item.ending_balance != 0 %}
                                    <span class="uf-amount uf-ending-amount {% if item.ending_balance > 0 %}uf-positive{% else %}uf-negative{% endif %}">
                                        {{ "{:,.2f}"|format(item.ending_balance|float) }}
                                    </span>
                                {% else %}
                                    <span class="uf-amount-zero">-</span>
                                {% endif %}
                            </td>
                            <td class="uf-operations-cell">
                                <div class="uf-btn-group-sm">
                                    <a href="{{ url_for('financial.detail_ledger', subject_id=item.id, start_date=start_date, end_date=end_date) }}"
                                       class="uf-btn uf-btn-xs uf-btn-info" title="查看明细账">
                                        <i class="fas fa-list"></i>
                                    </a>
                                    <button type="button" class="uf-btn uf-btn-xs uf-btn-secondary"
                                            onclick="showSubjectAnalysis({{ item.id }})" title="分析">
                                        <i class="fas fa-chart-line"></i>
                                    </button>
                                    <button type="button" class="uf-btn uf-btn-xs uf-btn-warning"
                                            onclick="exportSubjectData({{ item.id }})" title="导出">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}

                        {% if not general_ledger_data %}
                        <tr class="uf-empty-row">
                            <td colspan="10" class="uf-empty-cell">
                                <div class="uf-empty-content">
                                    <i class="fas fa-inbox uf-empty-icon"></i>
                                    <p class="uf-empty-text">没有找到相关数据</p>
                                    <p class="uf-empty-hint">请调整查询条件后重试</p>
                                </div>
                            </td>
                        </tr>
                        {% endif %}
                    </tbody>

                    {% if general_ledger_data %}
                    <tfoot>
                        <tr class="uf-total-row">
                            <th colspan="4">合计</th>
                            <th class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-total">{{ "%.2f"|format(total_opening|float) }}</span>
                            </th>
                            <th class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-total">{{ "%.2f"|format(total_debit|float) }}</span>
                            </th>
                            <th class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-total">{{ "%.2f"|format(total_credit|float) }}</span>
                            </th>
                            <th class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-total">{{ "%.2f"|format(total_ending|float) }}</span>
                            </th>
                            <th></th>
                        </tr>
                    </tfoot>
                    {% endif %}
                </table>
            </div>
        </div>
    </div>

    <!-- 试算平衡检查 -->
    {% if general_ledger_data %}
    <div class="uf-card">
        <div class="uf-card-header">
            <div class="uf-card-header-title">
                <i class="fas fa-balance-scale uf-card-header-icon"></i>
                试算平衡检查
            </div>
            <div class="uf-card-header-actions">
                {% set balance_diff = total_debit - total_credit %}
                {% if balance_diff == 0 %}
                    <span class="uf-status uf-status-success">✓ 试算平衡</span>
                {% else %}
                    <span class="uf-status uf-status-danger">✗ 试算不平衡</span>
                {% endif %}
            </div>
        </div>
        <div class="uf-card-body">
            <div class="uf-balance-check-grid">
                <div class="uf-balance-item">
                    <div class="uf-balance-label">本期借方发生额合计：</div>
                    <div class="uf-balance-value">
                        <span class="uf-currency">¥</span><span class="uf-amount uf-amount-large">{{ "%.2f"|format(total_debit|float) }}</span>
                    </div>
                </div>
                <div class="uf-balance-item">
                    <div class="uf-balance-label">本期贷方发生额合计：</div>
                    <div class="uf-balance-value">
                        <span class="uf-currency">¥</span><span class="uf-amount uf-amount-large">{{ "%.2f"|format(total_credit|float) }}</span>
                    </div>
                </div>
                <div class="uf-balance-item">
                    <div class="uf-balance-label">借贷差额：</div>
                    <div class="uf-balance-value">
                        {% set balance_diff = total_debit - total_credit %}
                        <span class="uf-currency">¥</span>
                        <span class="uf-amount uf-amount-large {% if balance_diff == 0 %}uf-amount-success{% else %}uf-amount-danger{% endif %}">
                            {{ "%.2f"|format(balance_diff|float) }}
                        </span>
                    </div>
                </div>
                <div class="uf-balance-item">
                    <div class="uf-balance-label">平衡状态：</div>
                    <div class="uf-balance-value">
                        {% if balance_diff == 0 %}
                            <span class="uf-status uf-status-success">✓ 试算平衡</span>
                        {% else %}
                            <span class="uf-status uf-status-danger">✗ 试算不平衡</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 按科目类型汇总 -->
    {% if general_ledger_data %}
    <div class="uf-card">
        <div class="uf-card-header">
            <div class="uf-card-header-title">
                <i class="fas fa-chart-pie uf-card-header-icon"></i>
                按科目类型汇总
            </div>
        </div>
        <div class="uf-card-body" style="padding: 0;">
            <div class="uf-table-container">
                <table class="uf-table uf-summary-table">
                    <thead>
                        <tr>
                            <th style="width: 200px;">科目类型</th>
                            <th class="uf-amount-col" style="width: 150px;">期末余额</th>
                            <th class="text-center" style="width: 100px;">科目数量</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% set type_summary = {} %}
                        {% for item in general_ledger_data %}
                            {% if item.subject_type not in type_summary %}
                                {% set _ = type_summary.update({item.subject_type: {'balance': 0, 'count': 0}}) %}
                            {% endif %}
                            {% set _ = type_summary[item.subject_type].update({
                                'balance': type_summary[item.subject_type]['balance'] + item.ending_balance,
                                'count': type_summary[item.subject_type]['count'] + 1
                            }) %}
                        {% endfor %}

                        {% for type_name, summary in type_summary.items() %}
                        <tr>
                            <td>
                                <span class="uf-status uf-status-info">{{ type_name }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount">{{ "%.2f"|format(summary.balance|float) }}</span>
                            </td>
                            <td class="text-center">
                                <span class="uf-count-badge">{{ summary.count }}</span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block financial_js %}
<script>
// 用友风格总账查询页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initUFGeneralLedger();
});

function initUFGeneralLedger() {
    // 初始化表格功能
    initTableFeatures();

    // 初始化表格排序
    initTableSorting();

    // 初始化快捷键
    initKeyboardShortcuts();

    // 初始化工具提示
    initTooltips();

    // 初始化筛选功能
    initTableFilters();

    // 初始化选择功能
    initRowSelection();
}

function initTableFeatures() {
    const table = document.getElementById('generalLedgerTable');
    if (table) {
        // 添加表格行悬停效果
        const rows = table.querySelectorAll('tbody tr.uf-ledger-row');
        rows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.classList.add('uf-row-hover');
            });
            row.addEventListener('mouseleave', function() {
                this.classList.remove('uf-row-hover');
            });

            // 双击查看明细账
            row.addEventListener('dblclick', function() {
                const subjectId = this.dataset.subjectId;
                if (subjectId) {
                    const startDate = document.getElementById('start_date').value;
                    const endDate = document.getElementById('end_date').value;
                    window.open(`/financial/ledgers/detail?subject_id=${subjectId}&start_date=${startDate}&end_date=${endDate}`, '_blank');
                }
            });
        });
    }
}

function initTableFilters() {
    // 科目搜索
    const subjectFilter = document.getElementById('subjectFilter');
    if (subjectFilter) {
        subjectFilter.addEventListener('input', debounce(filterTable, 300));
    }

    // 类型筛选
    const typeFilter = document.getElementById('typeFilter');
    if (typeFilter) {
        typeFilter.addEventListener('change', filterTable);
    }
}

function initRowSelection() {
    // 全选功能
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', toggleSelectAll);
    }

    // 行选择功能
    const rowCheckboxes = document.querySelectorAll('.uf-row-checkbox');
    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectAllState);
    });
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 表格筛选功能
function filterTable() {
    const subjectFilter = document.getElementById('subjectFilter').value.toLowerCase();
    const typeFilter = document.getElementById('typeFilter').value;
    const rows = document.querySelectorAll('#generalLedgerTable tbody tr.uf-ledger-row');

    let visibleCount = 0;

    rows.forEach(row => {
        const subjectCode = row.querySelector('.uf-subject-code').textContent.toLowerCase();
        const subjectName = row.querySelector('.uf-subject-name').textContent.toLowerCase();
        const subjectType = row.dataset.subjectType;

        const matchesSubject = subjectCode.includes(subjectFilter) || subjectName.includes(subjectFilter);
        const matchesType = !typeFilter || subjectType === typeFilter;

        if (matchesSubject && matchesType) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // 更新记录数显示
    updateRecordCount(visibleCount);
}

// 更新记录数显示
function updateRecordCount(count) {
    const recordCount = document.querySelector('.uf-record-count');
    if (recordCount) {
        recordCount.textContent = `共 ${count} 个科目`;
    }
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const rowCheckboxes = document.querySelectorAll('.uf-row-checkbox');

    rowCheckboxes.forEach(checkbox => {
        if (checkbox.closest('tr').style.display !== 'none') {
            checkbox.checked = selectAll.checked;
        }
    });
}

// 更新全选状态
function updateSelectAllState() {
    const selectAll = document.getElementById('selectAll');
    const visibleCheckboxes = Array.from(document.querySelectorAll('.uf-row-checkbox'))
        .filter(cb => cb.closest('tr').style.display !== 'none');
    const checkedCount = visibleCheckboxes.filter(cb => cb.checked).length;

    selectAll.checked = checkedCount === visibleCheckboxes.length && visibleCheckboxes.length > 0;
    selectAll.indeterminate = checkedCount > 0 && checkedCount < visibleCheckboxes.length;
}

// 选择所有行
function selectAllRows() {
    document.getElementById('selectAll').checked = true;
    toggleSelectAll();
}

// 清除选择
function clearSelection() {
    document.getElementById('selectAll').checked = false;
    toggleSelectAll();
}

// 快速查询
function quickQuery(type) {
    const today = new Date();
    let startDate, endDate;

    if (type === 'month') {
        startDate = new Date(today.getFullYear(), today.getMonth(), 1);
        endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    } else if (type === 'year') {
        startDate = new Date(today.getFullYear(), 0, 1);
        endDate = new Date(today.getFullYear(), 11, 31);
    }

    document.getElementById('start_date').value = startDate.toISOString().split('T')[0];
    document.getElementById('end_date').value = endDate.toISOString().split('T')[0];

    // 自动提交查询
    document.querySelector('.uf-query-form').submit();
}

// 刷新总账
function refreshGeneralLedger() {
    window.location.reload();
}

// 生成所有明细账
function generateAllLedgers() {
    if (!confirm('确定要批量生成所有科目的明细账吗？这可能需要一些时间。')) {
        return;
    }

    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;

    if (!startDate || !endDate) {
        alert('请先选择查询日期范围');
        return;
    }

    // 显示加载状态
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
    btn.disabled = true;

    // 这里应该调用后端API
    setTimeout(() => {
        alert('批量生成完成！');
        btn.innerHTML = originalText;
        btn.disabled = false;
        window.location.reload();
    }, 3000);
}

// 切换表格视图
function toggleTableView(tableType) {
    const container = document.getElementById(tableType + 'TableContainer');
    if (container) {
        container.classList.toggle('uf-fullscreen');
    }
}

// 刷新汇总表格
function refreshSummaryTable() {
    const form = document.querySelector('.uf-query-form');
    if (form) {
        form.submit();
    }
}

// 显示表格设置
function showTableSettings() {
    alert('表格设置功能开发中...');
}

// 显示科目分析
function showSubjectAnalysis(subjectId) {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    const url = `/financial/analysis/subject/${subjectId}?start_date=${startDate}&end_date=${endDate}`;
    window.open(url, '_blank', 'width=1000,height=700,scrollbars=yes,resizable=yes');
}

// 导出科目数据
function exportSubjectData(subjectId) {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    const url = `/financial/export/subject/${subjectId}?start_date=${startDate}&end_date=${endDate}`;
    window.open(url, '_blank');
}

// 批量导出选中科目
function batchExportSelected() {
    const selectedIds = Array.from(document.querySelectorAll('.uf-row-checkbox:checked'))
        .map(cb => cb.value);

    if (selectedIds.length === 0) {
        alert('请先选择要导出的科目');
        return;
    }

    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    const url = `/financial/export/batch?subject_ids=${selectedIds.join(',')}&start_date=${startDate}&end_date=${endDate}`;
    window.open(url, '_blank');
}

// 显示余额分析
function showBalanceAnalysis() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;

    if (!startDate || !endDate) {
        alert('请先选择查询日期范围');
        return;
    }

    const url = `/financial/analysis/balance?start_date=${startDate}&end_date=${endDate}`;
    window.open(url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
}

function initTableSorting() {
    // 为表头添加排序功能
    const headers = document.querySelectorAll('.uf-general-ledger-table th');
    headers.forEach((header, index) => {
        if (index < 8) { // 前8列可排序
            header.classList.add('sortable');
            header.addEventListener('click', () => sortTable(index));
        }
    });
}

function sortTable(columnIndex) {
    const table = document.querySelector('.uf-general-ledger-table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr:not(.empty-row)'));

    if (rows.length === 0) return;

    // 获取当前排序状态
    const header = table.querySelectorAll('th')[columnIndex];
    const isAsc = !header.classList.contains('sort-asc');

    // 清除所有排序标记
    table.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });

    // 设置当前排序标记
    header.classList.add(isAsc ? 'sort-asc' : 'sort-desc');

    // 排序行
    rows.sort((a, b) => {
        const aValue = a.cells[columnIndex].textContent.trim();
        const bValue = b.cells[columnIndex].textContent.trim();

        // 数字列排序
        if (columnIndex >= 4 && columnIndex <= 7) {
            const aNum = parseFloat(aValue.replace(/[¥,\-]/g, '')) || 0;
            const bNum = parseFloat(bValue.replace(/[¥,\-]/g, '')) || 0;
            return isAsc ? aNum - bNum : bNum - aNum;
        }

        // 文本列排序
        return isAsc ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    });

    // 重新插入排序后的行
    rows.forEach(row => tbody.appendChild(row));
}

function initKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+E: 导出
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            exportGeneralLedger();
        }

        // Ctrl+P: 打印
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printGeneralLedger();
        }

        // F5: 刷新查询
        if (e.key === 'F5') {
            e.preventDefault();
            document.querySelector('.uf-query-form').submit();
        }
    });
}

function initTooltips() {
    // 为金额单元格添加工具提示
    const amountCells = document.querySelectorAll('.uf-amount');
    amountCells.forEach(cell => {
        const value = cell.textContent.replace(/[¥,]/g, '');
        if (value && value !== '-') {
            cell.title = `金额：${value}`;
        }
    });
}

function exportGeneralLedger() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    const subjectType = document.getElementById('subject_type').value;

    if (!startDate || !endDate) {
        alert('请选择查询日期范围');
        return;
    }

    const url = `{{ url_for('financial.export_report', report_type='general_ledger') }}?start_date=${startDate}&end_date=${endDate}&subject_type=${subjectType}`;
    window.open(url, '_blank');
}

function printGeneralLedger() {
    // 创建打印窗口
    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent();

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
}

function generatePrintContent() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    const subjectType = document.getElementById('subject_type').value;

    const table = document.querySelector('.uf-general-ledger-table').outerHTML;

    return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>总账查询报表</title>
            <style>
                body { font-family: 'Microsoft YaHei', sans-serif; font-size: 12px; }
                .print-header { text-align: center; margin-bottom: 20px; }
                .print-info { margin-bottom: 10px; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #999; padding: 4px 6px; text-align: center; }
                th { background: #f0f8ff; font-weight: 600; }
                .uf-amount-col { text-align: right; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <div class="print-header">
                <h2>总账查询报表</h2>
                <div class="print-info">查询期间：${startDate} 至 ${endDate}</div>
                ${subjectType ? `<div class="print-info">科目类型：${subjectType}</div>` : ''}
                <div class="print-info">打印时间：${new Date().toLocaleString()}</div>
            </div>
            ${table}
        </body>
        </html>
    `;
}

function resetQueryForm() {
    const today = new Date().toISOString().split('T')[0];
    const firstDay = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];

    document.getElementById('start_date').value = firstDay;
    document.getElementById('end_date').value = today;
    document.getElementById('subject_type').value = '';
}
</script>
{% endblock %}

{% block financial_css %}
<style>
/* 用友风格总账查询页面专用样式 - 13px字体标准 */
.uf-general-ledger-container {
    background: var(--uf-light);
    min-height: 100vh;
    padding: 0;
    font-family: var(--uf-font-family);
    font-size: 13px;
}

/* 查询卡片样式 */
.uf-query-card {
    margin-bottom: 12px;
    border: 1px solid var(--uf-border);
    box-shadow: var(--uf-box-shadow);
}

/* 查询表单样式 */
.uf-query-form {
    margin: 0;
}

/* 查询条件和操作按钮一行布局 */
.uf-query-actions-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;
}

.uf-query-conditions {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
    min-width: 0;
}

.uf-query-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 0 0 auto;
}

.uf-form-group {
    display: flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
}

.uf-form-label {
    white-space: nowrap;
    font-size: 13px;
    color: var(--uf-gray-600);
    font-weight: 500;
}

.uf-form-control {
    min-width: 120px;
    height: 32px;
    font-size: 13px;
    padding: 4px 8px;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    background: var(--uf-white);
}

/* 汇总卡片样式 */
.uf-ledger-summary-card {
    border: 1px solid var(--uf-border);
    box-shadow: var(--uf-box-shadow);
}

.uf-card-header-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: var(--uf-primary);
}

.uf-card-header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.uf-record-count {
    font-size: 12px;
    color: var(--uf-muted);
}

.uf-header-tools {
    display: flex;
    gap: 4px;
}

/* 表格工具栏样式 */
.uf-table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: var(--uf-gray-50);
    border-bottom: 1px solid var(--uf-border);
    gap: 16px;
    flex-wrap: wrap;
}

.uf-table-filters {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.uf-filter-input {
    width: 200px;
    height: 28px;
    font-size: 12px;
    padding: 4px 8px;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
}

.uf-filter-select {
    width: 120px;
    height: 28px;
    font-size: 12px;
    padding: 4px 8px;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
}

.uf-table-actions {
    display: flex;
    gap: 6px;
    align-items: center;
}

/* 表格容器样式 */
.uf-table-container {
    overflow: auto;
    max-height: 70vh;
    border: 1px solid var(--uf-border);
}

.uf-table-container.uf-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    max-height: 100vh;
    background: var(--uf-white);
}

/* 总账表格专用样式 - 13px字体标准 */
.uf-general-ledger-table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--uf-font-family);
    font-size: 13px;
    background: var(--uf-white);
    table-layout: fixed;
}

.uf-general-ledger-table th {
    background: linear-gradient(to bottom, var(--uf-primary) 0%, var(--uf-primary-dark) 100%);
    color: white;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 10;
    padding: 8px 6px;
    height: 36px;
    font-size: 13px;
    border: 1px solid var(--uf-grid-border);
}

.uf-general-ledger-table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.uf-general-ledger-table th.sortable:hover {
    background: linear-gradient(to bottom, var(--uf-primary-light) 0%, var(--uf-primary) 100%);
}

.uf-sort-icon {
    margin-left: 4px;
    font-size: 11px;
    opacity: 0.7;
}

.uf-sort-icon.uf-sort-active {
    opacity: 1;
    color: #fff;
}

/* 表格列宽定义 */
.uf-col-checkbox { width: 40px; }
.uf-col-code { width: 100px; }
.uf-col-name { width: auto; min-width: 200px; }
.uf-col-type { width: 80px; }
.uf-col-direction { width: 80px; }
.uf-col-opening { width: 120px; }
.uf-col-debit { width: 120px; }
.uf-col-credit { width: 120px; }
.uf-col-ending { width: 120px; }
.uf-col-operations { width: 100px; }

/* 表格行样式 */
.uf-general-ledger-table td {
    padding: 6px 8px;
    vertical-align: middle;
    border: 1px solid var(--uf-grid-border);
    height: 36px;
    font-size: 13px;
    line-height: 1.3;
}

.uf-ledger-row:hover {
    background: var(--uf-row-hover) !important;
}

.uf-ledger-row:nth-child(even) {
    background: var(--uf-gray-50);
}

/* 单元格样式 */
.uf-checkbox-cell {
    text-align: center;
    padding: 4px;
}

.uf-subject-code {
    text-align: center;
}

.uf-code-text {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--uf-primary);
    font-size: 12px;
}

.uf-subject-name {
    text-align: left;
    padding-left: 12px;
}

.uf-name-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.uf-name-text {
    font-weight: 500;
    color: var(--uf-gray-700);
}

.uf-name-desc {
    font-size: 11px;
    color: var(--uf-muted);
    font-style: italic;
}

.uf-type-cell {
    text-align: center;
}

.uf-direction-cell {
    text-align: center;
}

.uf-direction {
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
}

.uf-direction-debit {
    background: var(--uf-info);
    color: white;
}

.uf-direction-credit {
    background: var(--uf-warning);
    color: white;
}

/* 金额单元格样式 */
.uf-amount-cell {
    text-align: right;
    padding-right: 12px;
    font-family: 'Courier New', 'Times New Roman', monospace;
    font-size: 13px;
    white-space: nowrap;
}

.uf-amount {
    font-weight: 500;
}

.uf-amount.uf-positive {
    color: var(--uf-amount-positive);
}

.uf-amount.uf-negative {
    color: var(--uf-amount-negative);
}

.uf-amount-zero {
    color: var(--uf-muted);
    font-style: italic;
}

.uf-debit-amount {
    color: var(--uf-info);
}

.uf-credit-amount {
    color: var(--uf-warning);
}

.uf-ending-amount {
    font-weight: 600;
}

.uf-ending-cell {
    background: var(--uf-gray-50);
}

/* 操作单元格样式 */
.uf-operations-cell {
    text-align: center;
}

.uf-btn-group-sm {
    display: flex;
    gap: 2px;
    justify-content: center;
}

.uf-btn-xs {
    padding: 2px 6px;
    font-size: 11px;
    line-height: 1.2;
    border-radius: 2px;
}

/* 空数据样式 */
.uf-empty-row {
    background: var(--uf-gray-50);
}

.uf-empty-cell {
    text-align: center;
    padding: 40px 20px;
}

.uf-empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.uf-empty-icon {
    font-size: 48px;
    color: var(--uf-muted);
    opacity: 0.5;
}

.uf-empty-text {
    font-size: 16px;
    color: var(--uf-gray-600);
    margin: 0;
}

.uf-empty-hint {
    font-size: 12px;
    color: var(--uf-muted);
    margin: 0;
}

/* 科目编码和名称样式 */
.uf-subject-code {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--uf-primary);
    font-size: 11px;
}

.uf-subject-name {
    font-weight: 500;
    color: #333;
}

/* 金额显示样式 */
.uf-amount-zero {
    color: var(--uf-muted);
    font-style: italic;
}

.uf-amount-bold {
    font-weight: 600;
    color: #333;
}

.uf-amount-large {
    font-size: 13px;
    font-weight: 600;
}

.uf-amount-success {
    color: var(--uf-success);
}

.uf-amount-danger {
    color: var(--uf-danger);
}

.uf-amount-total {
    font-weight: 700;
    color: var(--uf-primary);
}

/* 合计行样式 */
.uf-total-row {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    font-weight: 600;
}

.uf-total-row th {
    background: linear-gradient(to bottom, #e6f2ff 0%, #d9e8ff 100%);
    color: var(--uf-primary);
    font-weight: 700;
}

/* 试算平衡检查样式 */
.uf-balance-check-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    padding: 8px;
}

.uf-balance-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
}

.uf-balance-label {
    font-weight: 500;
    color: #333;
    font-size: 11px;
}

.uf-balance-value {
    font-weight: 600;
    font-size: 12px;
}

/* 汇总表样式 */
.uf-summary-table {
    font-size: 11px;
}

.uf-summary-table th {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
}

.uf-count-badge {
    display: inline-block;
    background: var(--uf-primary);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 13px;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

/* 记录数量显示 */
.uf-record-count {
    font-size: 11px;
    color: var(--uf-muted);
    font-weight: normal;
}

/* 状态标签样式扩展 */
.uf-status-primary {
    background: var(--uf-primary);
    color: white;
    border-color: var(--uf-primary);
}

.uf-status-info {
    background: var(--uf-info);
    color: white;
    border-color: var(--uf-info);
}

.uf-status-warning {
    background: var(--uf-warning);
    color: #333;
    border-color: var(--uf-warning);
}

.uf-status-success {
    background: var(--uf-success);
    color: white;
    border-color: var(--uf-success);
}

.uf-status-danger {
    background: var(--uf-danger);
    color: white;
    border-color: var(--uf-danger);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .uf-query-actions-row {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .uf-query-conditions {
        justify-content: center;
    }

    .uf-query-actions {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .uf-query-conditions {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .uf-form-group {
        width: 100%;
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .uf-form-control {
        min-width: auto;
        width: 100%;
    }

    .uf-query-actions {
        flex-wrap: wrap;
        gap: 6px;
    }

    .uf-balance-check-grid {
        grid-template-columns: 1fr;
    }

    .uf-general-ledger-table {
        font-size: 13px;
    }

    .uf-general-ledger-table th,
    .uf-general-ledger-table td {
        padding: 2px 4px;
    }
}

/* 打印样式 */
@media print {
    .uf-card-header,
    .uf-btn,
    .page-actions {
        display: none !important;
    }

    .uf-card {
        border: none;
        box-shadow: none;
        margin: 0;
    }

    .uf-card-body {
        padding: 0;
    }

    .uf-general-ledger-table {
        font-size: 13px;
    }

    .uf-general-ledger-table th,
    .uf-general-ledger-table td {
        padding: 2px 4px;
    }
}
</style>
{% endblock %}
