<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财务凭证导出功能对比测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
            border-bottom: 1px solid #ccc;
            padding-bottom: 10px;
        }
        .button-group {
            display: flex;
            gap: 15px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            transition: all 0.3s;
        }
        .btn-print {
            background-color: #28a745;
            color: white;
        }
        .btn-print:hover {
            background-color: #218838;
        }
        .btn-pdf {
            background-color: #dc3545;
            color: white;
        }
        .btn-pdf:hover {
            background-color: #c82333;
        }
        .btn-excel {
            background-color: #17a2b8;
            color: white;
        }
        .btn-excel:hover {
            background-color: #138496;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .status-good {
            color: #28a745;
            font-weight: bold;
        }
        .status-fixed {
            color: #007bff;
            font-weight: bold;
        }
        .status-issue {
            color: #dc3545;
            font-weight: bold;
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .note h3 {
            margin-top: 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>财务凭证导出功能对比测试</h1>
            <p>测试打印模块与PDF/Excel导出功能的一致性</p>
        </div>

        <div class="test-section">
            <h2>测试凭证 - ID: 17 (CYQSYZXPZ20250611003)</h2>
            <div class="button-group">
                <a href="http://xiaoyuanst.com/financial/vouchers/17/print?preview=1" 
                   class="test-btn btn-print" target="_blank">
                    📄 打印预览 (原版)
                </a>
                <a href="http://xiaoyuanst.com/financial/vouchers/17/export/pdf" 
                   class="test-btn btn-pdf" target="_blank">
                    📑 导出PDF (修复版)
                </a>
                <a href="http://xiaoyuanst.com/financial/vouchers/17/export/excel" 
                   class="test-btn btn-excel" target="_blank">
                    📊 导出Excel (修复版)
                </a>
            </div>
        </div>

        <div class="test-section">
            <h2>测试凭证 - ID: 18 (CYQSYZXPZ20250611004)</h2>
            <div class="button-group">
                <a href="http://xiaoyuanst.com/financial/vouchers/18/print?preview=1" 
                   class="test-btn btn-print" target="_blank">
                    📄 打印预览 (原版)
                </a>
                <a href="http://xiaoyuanst.com/financial/vouchers/18/export/pdf" 
                   class="test-btn btn-pdf" target="_blank">
                    📑 导出PDF (修复版)
                </a>
                <a href="http://xiaoyuanst.com/financial/vouchers/18/export/excel" 
                   class="test-btn btn-excel" target="_blank">
                    📊 导出Excel (修复版)
                </a>
            </div>
        </div>

        <div class="test-section">
            <h2>测试凭证 - ID: 19 (CYQSYZXPZ20250611005)</h2>
            <div class="button-group">
                <a href="http://xiaoyuanst.com/financial/vouchers/19/print?preview=1" 
                   class="test-btn btn-print" target="_blank">
                    📄 打印预览 (原版)
                </a>
                <a href="http://xiaoyuanst.com/financial/vouchers/19/export/pdf" 
                   class="test-btn btn-pdf" target="_blank">
                    📑 导出PDF (修复版)
                </a>
                <a href="http://xiaoyuanst.com/financial/vouchers/19/export/excel" 
                   class="test-btn btn-excel" target="_blank">
                    📊 导出Excel (修复版)
                </a>
            </div>
        </div>

        <div class="note">
            <h3>修复内容说明</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>功能项</th>
                        <th>打印模块 (原版)</th>
                        <th>PDF导出 (修复前)</th>
                        <th>PDF导出 (修复后)</th>
                        <th>Excel导出 (修复前)</th>
                        <th>Excel导出 (修复后)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>数据过滤</td>
                        <td class="status-good">✓ 过滤空行</td>
                        <td class="status-issue">✗ 包含空行</td>
                        <td class="status-fixed">✓ 过滤空行</td>
                        <td class="status-issue">✗ 包含空行</td>
                        <td class="status-fixed">✓ 过滤空行</td>
                    </tr>
                    <tr>
                        <td>金额格式</td>
                        <td class="status-good">✓ 千分位+元</td>
                        <td class="status-issue">✗ 简单小数</td>
                        <td class="status-fixed">✓ 千分位+元</td>
                        <td class="status-issue">✗ 简单数字</td>
                        <td class="status-fixed">✓ 千分位格式</td>
                    </tr>
                    <tr>
                        <td>凭证信息</td>
                        <td class="status-good">✓ 完整信息</td>
                        <td class="status-issue">✗ 信息不全</td>
                        <td class="status-fixed">✓ 完整信息</td>
                        <td class="status-issue">✗ 表格格式</td>
                        <td class="status-fixed">✓ 凭证格式</td>
                    </tr>
                    <tr>
                        <td>签名栏</td>
                        <td class="status-good">✓ 完整签名</td>
                        <td class="status-issue">✗ 只有标签</td>
                        <td class="status-fixed">✓ 完整签名</td>
                        <td class="status-issue">✗ 无签名栏</td>
                        <td class="status-fixed">✓ 完整签名</td>
                    </tr>
                    <tr>
                        <td>大写金额</td>
                        <td class="status-good">✓ 自动计算</td>
                        <td class="status-issue">✗ 无大写</td>
                        <td class="status-fixed">✓ 自动计算</td>
                        <td class="status-issue">✗ 无大写</td>
                        <td class="status-good">- 不适用</td>
                    </tr>
                    <tr>
                        <td>专业样式</td>
                        <td class="status-good">✓ 用友风格</td>
                        <td class="status-issue">✗ 简单样式</td>
                        <td class="status-fixed">✓ 用友风格</td>
                        <td class="status-issue">✗ 表格样式</td>
                        <td class="status-fixed">✓ 财务格式</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="note">
            <h3>测试步骤</h3>
            <ol>
                <li>点击"打印预览"查看原版打印模块的效果</li>
                <li>点击"导出PDF"下载修复后的PDF文件，对比格式是否一致</li>
                <li>点击"导出Excel"下载修复后的Excel文件，检查是否采用财务凭证格式</li>
                <li>重点检查：数据过滤、金额格式、签名信息、整体布局是否与打印模块一致</li>
            </ol>
        </div>
    </div>
</body>
</html>
