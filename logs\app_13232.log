2025-06-21 17:31:23,596 INFO: 应用启动 - PID: 13232 [in C:\StudentsCMSSP\app\__init__.py:862]
2025-06-21 17:31:27,917 ERROR: Exception on /financial/ledgers/general [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\ledgers.py", line 234, in general_ledger
    return render_template('financial/ledgers/general.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\financial\ledgers\general.html", line 1, in top-level template code
    {% extends "financial/base.html" %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 429, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 131, in block 'content'
    {{ self.financial_content() }}
  File "C:\StudentsCMSSP\app\templates\financial\ledgers\general.html", line 227, in block 'financial_content'
    <span class="uf-amount uf-debit-amount">{{ "{:,.2f}"|format(item.period_debit|float) }}</span>
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\filters.py", line 1016, in do_format
    return soft_str(value) % (kwargs or args)
TypeError: not all arguments converted during string formatting
2025-06-21 17:31:30,320 ERROR: Exception on /financial/ledgers/detail [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
pyodbc.ProgrammingError: ('42S02', "[42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]对象名 'detail_ledgers' 无效。 (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\ledgers.py", line 40, in detail_ledger
    generated_ledgers = DetailLedger.query.filter(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2673, in all
    return self._iter().all()  # type: ignore
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2827, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2351, in execute
    return self._execute_internal(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2236, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\context.py", line 293, in orm_execute_statement
    result = conn.execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1418, in execute
    return meth(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1640, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2353, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.ProgrammingError: (pyodbc.ProgrammingError) ('42S02', "[42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]对象名 'detail_ledgers' 无效。 (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: SELECT TOP 50 detail_ledgers.area_id AS detail_ledgers_area_id, detail_ledgers.subject_id AS detail_ledgers_subject_id, detail_ledgers.year AS detail_ledgers_year, detail_ledgers.month AS detail_ledgers_month, detail_ledgers.ledger_date AS detail_ledgers_ledger_date, detail_ledgers.opening_balance AS detail_ledgers_opening_balance, detail_ledgers.closing_balance AS detail_ledgers_closing_balance, detail_ledgers.period_debit AS detail_ledgers_period_debit, detail_ledgers.period_credit AS detail_ledgers_period_credit, detail_ledgers.record_count AS detail_ledgers_record_count, detail_ledgers.status AS detail_ledgers_status, detail_ledgers.generated_by AS detail_ledgers_generated_by, detail_ledgers.generated_at AS detail_ledgers_generated_at, detail_ledgers.notes AS detail_ledgers_notes, detail_ledgers.id AS detail_ledgers_id, detail_ledgers.created_at AS detail_ledgers_created_at, detail_ledgers.updated_at AS detail_ledgers_updated_at 
FROM detail_ledgers 
WHERE detail_ledgers.area_id = ? ORDER BY detail_ledgers.year DESC, detail_ledgers.month DESC, detail_ledgers.created_at DESC]
[parameters: (44,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-21 17:31:57,794 ERROR: Exception on /financial/ledgers/detail [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
pyodbc.ProgrammingError: ('42S02', "[42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]对象名 'detail_ledgers' 无效。 (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\ledgers.py", line 40, in detail_ledger
    generated_ledgers = DetailLedger.query.filter(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2673, in all
    return self._iter().all()  # type: ignore
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2827, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2351, in execute
    return self._execute_internal(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2236, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\context.py", line 293, in orm_execute_statement
    result = conn.execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1418, in execute
    return meth(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1640, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2353, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.ProgrammingError: (pyodbc.ProgrammingError) ('42S02', "[42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]对象名 'detail_ledgers' 无效。 (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: SELECT TOP 50 detail_ledgers.area_id AS detail_ledgers_area_id, detail_ledgers.subject_id AS detail_ledgers_subject_id, detail_ledgers.year AS detail_ledgers_year, detail_ledgers.month AS detail_ledgers_month, detail_ledgers.ledger_date AS detail_ledgers_ledger_date, detail_ledgers.opening_balance AS detail_ledgers_opening_balance, detail_ledgers.closing_balance AS detail_ledgers_closing_balance, detail_ledgers.period_debit AS detail_ledgers_period_debit, detail_ledgers.period_credit AS detail_ledgers_period_credit, detail_ledgers.record_count AS detail_ledgers_record_count, detail_ledgers.status AS detail_ledgers_status, detail_ledgers.generated_by AS detail_ledgers_generated_by, detail_ledgers.generated_at AS detail_ledgers_generated_at, detail_ledgers.notes AS detail_ledgers_notes, detail_ledgers.id AS detail_ledgers_id, detail_ledgers.created_at AS detail_ledgers_created_at, detail_ledgers.updated_at AS detail_ledgers_updated_at 
FROM detail_ledgers 
WHERE detail_ledgers.area_id = ? ORDER BY detail_ledgers.year DESC, detail_ledgers.month DESC, detail_ledgers.created_at DESC]
[parameters: (44,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-21 17:53:56,019 WARNING: [安全监控] 2025-06-21 17:53:56 - 可疑请求 | IP: ************ | 路径: /p-scanner.research.netd.cs.tu-dresden.de:443, 指标: 可疑HTTP方法 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 17:54:51,070 WARNING: [安全监控] 2025-06-21 17:54:51 - 可疑请求 | IP: *************** | 路径: /www.voanews.com:443, 指标: 可疑HTTP方法 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 17:54:52,620 WARNING: [安全监控] 2025-06-21 17:54:52 - 可疑请求 | IP: ************** | 路径: /www.baidu.com:443, 指标: 可疑HTTP方法 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 17:54:53,773 WARNING: [安全监控] 2025-06-21 17:54:53 - 可疑请求 | IP: ************** | 路径: /cn.bing.com:443, 指标: 可疑HTTP方法 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 17:54:55,148 WARNING: [安全监控] 2025-06-21 17:54:55 - 可疑请求 | IP: ************** | 路径: /www.so.com:443, 指标: 可疑HTTP方法 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 18:22:10,817 WARNING: [安全监控] 2025-06-21 18:22:10 - 可疑请求 | IP: *************:57642 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 19:07:06,449 WARNING: [安全监控] 2025-06-21 19:07:06 - 可疑请求 | IP: **************:34838 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 19:11:45,718 ERROR: Exception on /financial/ledgers/general [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\ledgers.py", line 234, in general_ledger
    # 计算汇总数据
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\financial\ledgers\general.html", line 1, in top-level template code
    {% extends "financial/base.html" %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 429, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 131, in block 'content'
    {{ self.financial_content() }}
  File "C:\StudentsCMSSP\app\templates\financial\ledgers\general.html", line 227, in block 'financial_content'
    <span class="uf-amount uf-debit-amount">{{ "{:,.2f}".format(item.period_debit|float|default(0)) }}</span>
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\filters.py", line 1016, in do_format
    return soft_str(value) % (kwargs or args)
TypeError: not all arguments converted during string formatting
2025-06-21 19:12:05,557 ERROR: Exception on /financial/ledgers/detail [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
pyodbc.ProgrammingError: ('42S02', "[42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]对象名 'detail_ledgers' 无效。 (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\ledgers.py", line 40, in detail_ledger
    generated_ledgers = DetailLedger.query.filter(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2673, in all
    return self._iter().all()  # type: ignore
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2827, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2351, in execute
    return self._execute_internal(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2236, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\context.py", line 293, in orm_execute_statement
    result = conn.execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1418, in execute
    return meth(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1640, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2353, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.ProgrammingError: (pyodbc.ProgrammingError) ('42S02', "[42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]对象名 'detail_ledgers' 无效。 (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: SELECT TOP 50 detail_ledgers.area_id AS detail_ledgers_area_id, detail_ledgers.subject_id AS detail_ledgers_subject_id, detail_ledgers.year AS detail_ledgers_year, detail_ledgers.month AS detail_ledgers_month, detail_ledgers.ledger_date AS detail_ledgers_ledger_date, detail_ledgers.opening_balance AS detail_ledgers_opening_balance, detail_ledgers.closing_balance AS detail_ledgers_closing_balance, detail_ledgers.period_debit AS detail_ledgers_period_debit, detail_ledgers.period_credit AS detail_ledgers_period_credit, detail_ledgers.record_count AS detail_ledgers_record_count, detail_ledgers.status AS detail_ledgers_status, detail_ledgers.generated_by AS detail_ledgers_generated_by, detail_ledgers.generated_at AS detail_ledgers_generated_at, detail_ledgers.notes AS detail_ledgers_notes, detail_ledgers.id AS detail_ledgers_id, detail_ledgers.created_at AS detail_ledgers_created_at, detail_ledgers.updated_at AS detail_ledgers_updated_at 
FROM detail_ledgers 
WHERE detail_ledgers.area_id = ? ORDER BY detail_ledgers.year DESC, detail_ledgers.month DESC, detail_ledgers.created_at DESC]
[parameters: (44,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-21 19:16:57,279 WARNING: [安全监控] 2025-06-21 19:16:57 - 可疑请求 | IP: ************* | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 19:44:50,477 WARNING: [安全监控] 2025-06-21 19:44:50 - 可疑请求 | IP: ************** | 路径: /google.com:443, 指标: 无User-Agent, 可疑HTTP方法 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 19:57:26,544 WARNING: [安全监控] 2025-06-21 19:57:26 - 可疑请求 | IP: ***********:54918 | 路径: /xmlrpc.php, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 20:04:43,506 WARNING: [安全监控] 2025-06-21 20:04:43 - 可疑请求 | IP: ***********:58147 | 路径: /xmlrpc.php, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 20:13:48,100 WARNING: [安全监控] 2025-06-21 20:13:48 - 可疑请求 | IP: *************:57673 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 20:32:47,624 WARNING: [安全监控] 2025-06-21 20:32:47 - 可疑请求 | IP: ************** | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 20:32:57,968 WARNING: [安全监控] 2025-06-21 20:32:57 - 可疑请求 | IP: **************:43634 | 路径: /setup.cgi, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 20:35:22,592 WARNING: [安全监控] 2025-06-21 20:35:22 - 可疑请求 | IP: ************:62780 | 路径: /xmlrpc.php, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 20:46:00,413 WARNING: [安全监控] 2025-06-21 20:46:00 - 可疑请求 | IP: **************:43772 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 21:07:45,657 WARNING: [安全监控] 2025-06-21 21:07:45 - 可疑请求 | IP: ************* | 路径: /***************:7227, 指标: 可疑HTTP方法 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 21:13:58,897 WARNING: [安全监控] 2025-06-21 21:13:58 - 可疑请求 | IP: ************* | 路径: /boaform/admin/formLogin, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 21:30:52,795 WARNING: [安全监控] 2025-06-21 21:30:52 - 可疑请求 | IP: **************:51760 | 路径: /xmlrpc.php, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 21:35:57,742 WARNING: [安全监控] 2025-06-21 21:35:57 - 可疑请求 | IP: *************:40508 | 路径: /wp-admin/setup-config.php, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 21:36:37,157 WARNING: [安全监控] 2025-06-21 21:36:37 - 可疑请求 | IP: *************:25222 | 路径: /wordpress/wp-admin/setup-config.php, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 21:54:26,152 WARNING: [安全监控] 2025-06-21 21:54:26 - 可疑请求 | IP: ************* | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 21:55:31,166 WARNING: [安全监控] 2025-06-21 21:55:31 - 可疑请求 | IP: *************:43796 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 22:01:02,777 WARNING: [安全监控] 2025-06-21 22:01:02 - 可疑请求 | IP: ************** | 路径: /google.com:443, 指标: 可疑HTTP方法 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 22:48:41,206 WARNING: [安全监控] 2025-06-21 22:48:41 - 可疑请求 | IP: ************** | 路径: /example.com:443, 指标: 可疑HTTP方法 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 22:48:46,596 WARNING: [安全监控] 2025-06-21 22:48:46 - 可疑请求 | IP: ************** | 路径: /example.com:443, 指标: 可疑HTTP方法 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 22:56:11,161 WARNING: [安全监控] 2025-06-21 22:56:11 - 可疑请求 | IP: ************* | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 23:08:53,119 WARNING: [安全监控] 2025-06-21 23:08:53 - 可疑请求 | IP: ************* | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 23:11:35,071 WARNING: [安全监控] 2025-06-21 23:11:35 - 可疑请求 | IP: ************* | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 23:39:05,877 WARNING: [安全监控] 2025-06-21 23:39:05 - 可疑请求 | IP: **************:51248 | 路径: /.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-21 23:43:30,241 WARNING: [安全监控] 2025-06-21 23:43:30 - 可疑请求 | IP: *************:39850 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
