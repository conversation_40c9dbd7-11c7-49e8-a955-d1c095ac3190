{% extends 'financial/base.html' %}

{% block title %}
{% if mode == 'view' %}
查看记账凭证 - {{ super() }}
{% elif mode == 'edit' %}
编辑记账凭证 - {{ super() }}
{% else %}
新建记账凭证 - {{ super() }}
{% endif %}
{% endblock %}

{% block styles %}
{{ super() }}
<!-- 重新载入用友主题样式，确保优先级 -->
<link rel="stylesheet" href="{{ url_for('static', filename='financial/css/yonyou-theme.css') }}?v=2.1.0">
<!-- 载入凭证专用样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='financial/css/voucher-style.css') }}?v=1.0.0">

<!-- 用友风格增强样式 -->
<style>
/* 消息提示动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 按钮悬停效果 */
.uf-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transition: all 0.2s ease;
}

/* 输入框聚焦效果 */
.uf-form-control:focus {
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
    transition: all 0.2s ease;
}

/* 表格行悬停效果 */
.uf-voucher-row:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

/* 科目选择器动画 */
.uf-subject-dropdown {
    animation: fadeInDown 0.3s ease-out;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载状态 */
.uf-loading {
    position: relative;
    pointer-events: none;
}

.uf-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

{% endblock %}

{% block page_title %}
{% if mode == 'view' %}
查看记账凭证
{% elif mode == 'edit' %}
编辑记账凭证
{% else %}
新建记账凭证
{% endif %}
{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-separator">/</span>
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.vouchers_index') }}">记账凭证</a></span>
<span class="uf-breadcrumb-separator">/</span>
<span class="uf-breadcrumb-item active">
{% if mode == 'view' %}
查看凭证
{% elif mode == 'edit' %}
编辑凭证
{% else %}
新增凭证
{% endif %}
</span>
{% endblock %}

{% block financial_content %}
<!-- 用友风格记账凭证编辑器 -->
<div class="uf-voucher-editor">
    <!-- 凭证窗口 -->
    <div class="uf-voucher-window">
        <!-- 窗口标题栏 -->
        <div class="uf-window-header">
            <div class="uf-window-title">
                <i class="uf-icon">📋</i>
                <span>记账凭证</span>
                {% if voucher %}
                    <span class="uf-status-badge uf-status-{{ voucher.status|lower }}">{{ voucher.status }}</span>
                {% endif %}
            </div>
            <div class="uf-window-controls">
                {% if mode == 'view' %}
                <button class="uf-btn uf-btn-sm" onclick="location.href='{{ url_for('financial.edit_voucher_unified', id=voucher.id) }}'">
                    <i class="uf-icon">✏️</i> 编辑
                </button>
                {% endif %}
                {% if mode == 'edit' %}
                <button class="uf-btn uf-btn-sm" onclick="location.href='{{ url_for('financial.view_voucher', id=voucher.id) }}'">
                    <i class="uf-icon">👁️</i> 查看
                </button>
                {% endif %}
                <button class="uf-btn uf-btn-sm" onclick="location.href='{{ url_for('financial.vouchers_index') }}'">
                    <i class="uf-icon">📋</i> 列表
                </button>
                <button class="uf-btn uf-btn-sm" onclick="window.close()">
                    <i class="uf-icon">✕</i> 关闭
                </button>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="uf-toolbar">
            <!-- 凭证导航组 -->
            <div class="uf-toolbar-group uf-navigation-group">
                <button class="uf-btn uf-btn-nav" onclick="navigateToFirst()" title="首张凭证">
                    <i class="uf-icon">⏮️</i> 首张
                </button>
                <button class="uf-btn uf-btn-nav" onclick="navigateToPrevious()" title="上一张凭证">
                    <i class="uf-icon">⏪</i> 上一张
                </button>
                <div class="uf-voucher-number-input">
                    <input type="text" class="uf-form-control uf-nav-input" id="nav-voucher-number"
                           placeholder="凭证号" title="输入凭证号跳转">
                    <button class="uf-btn uf-btn-nav" onclick="navigateToVoucher()" title="跳转到指定凭证">
                        <i class="uf-icon">�</i>
                    </button>
                </div>
                <button class="uf-btn uf-btn-nav" onclick="navigateToNext()" title="下一张凭证">
                    <i class="uf-icon">⏩</i> 下一张
                </button>
                <button class="uf-btn uf-btn-nav" onclick="navigateToLast()" title="末张凭证">
                    <i class="uf-icon">⏭️</i> 末张
                </button>
            </div>

            <div class="uf-toolbar-separator"></div>

            <!-- 凭证操作组 -->
            <div class="uf-toolbar-group">
                <button class="uf-btn uf-btn-primary" onclick="newVoucher()" title="新增凭证">
                    <i class="uf-icon">📄</i> 新增
                </button>
                {% if mode != 'view' %}
                <button class="uf-btn uf-btn-primary" onclick="saveVoucher()">
                    <i class="uf-icon">�</i> 保存
                </button>
                {% endif %}
                {% if voucher %}
                <button class="uf-btn" onclick="copyVoucher()" title="复制凭证">
                    <i class="uf-icon">📋</i> 复制
                </button>
                <button class="uf-btn" onclick="reverseVoucher()" title="冲销凭证">
                    <i class="uf-icon">🔄</i> 冲销
                </button>
                {% endif %}
            </div>

            <div class="uf-toolbar-separator"></div>

            <!-- 编辑操作组 -->
            {% if mode != 'view' %}
            <div class="uf-toolbar-group">
                <button class="uf-btn" onclick="addRow()">
                    <i class="uf-icon">➕</i> 增行
                </button>
                <button class="uf-btn" onclick="deleteRow()">
                    <i class="uf-icon">➖</i> 删行
                </button>
                <button class="uf-btn" onclick="insertRow()">
                    <i class="uf-icon">📝</i> 插行
                </button>
            </div>

            <div class="uf-toolbar-separator"></div>
            {% endif %}

            <!-- 打印和审核组 -->
            <div class="uf-toolbar-group">
                <button class="uf-btn" onclick="printVoucher()" title="打印凭证">
                    <i class="uf-icon">🖨️</i> 打印
                </button>
                <button class="uf-btn" onclick="printPreview()" title="打印预览">
                    <i class="uf-icon">👁️</i> 预览
                </button>
                <button class="uf-btn" onclick="checkBalance()">
                    <i class="uf-icon">⚖️</i> 平衡
                </button>
                {% if voucher and voucher.status == '待审核' %}
                <button class="uf-btn uf-btn-success" onclick="reviewVoucher()">
                    <i class="uf-icon">✅</i> 审核
                </button>
                {% endif %}
                {% if voucher and voucher.status == '已审核' %}
                <button class="uf-btn uf-btn-warning" onclick="unReviewVoucher()">
                    <i class="uf-icon">↩️</i> 取消审核
                </button>
                {% endif %}
            </div>

            <div class="uf-toolbar-separator"></div>

            <!-- 状态指示器 -->
            <div class="uf-balance-indicator">
                <span class="uf-balance-label">借贷平衡:</span>
                <span class="uf-balance-status" id="balance-indicator">未检查</span>
            </div>
        </div>

        <!-- 凭证信息栏 -->
        <div class="uf-voucher-info">
            <div class="uf-info-row">
                <!-- 凭证字 -->
                <div class="uf-form-group">
                    <label class="uf-form-label">凭证字:</label>
                    <select class="uf-form-control uf-voucher-type" id="voucher-type" {% if mode == 'view' %}disabled{% endif %}>
                        <option value="记" {% if voucher and voucher.voucher_type == '记' %}selected{% endif %}>记</option>
                        <option value="收" {% if voucher and voucher.voucher_type == '收' %}selected{% endif %}>收</option>
                        <option value="付" {% if voucher and voucher.voucher_type == '付' %}selected{% endif %}>付</option>
                        <option value="转" {% if voucher and voucher.voucher_type == '转' %}selected{% endif %}>转</option>
                    </select>
                </div>

                <!-- 凭证号 -->
                <div class="uf-form-group">
                    <label class="uf-form-label">号:</label>
                    <input type="text" class="uf-form-control uf-voucher-number" id="voucher-number"
                           value="{% if voucher %}{{ voucher.voucher_number }}{% else %}自动{% endif %}"
                           {% if mode == 'view' %}readonly{% endif %} placeholder="自动">
                </div>

                <!-- 日期 -->
                <div class="uf-form-group">
                    <label class="uf-form-label">日期:</label>
                    <input type="date" class="uf-form-control uf-voucher-date" id="voucher-date"
                           value="{% if voucher %}{{ voucher.voucher_date }}{% else %}{{ today }}{% endif %}"
                           {% if mode == 'view' %}readonly{% endif %}>
                </div>

                <!-- 附件 -->
                <div class="uf-form-group">
                    <label class="uf-form-label">附件:</label>
                    <div class="uf-attachment-input-group">
                        <input type="number" class="uf-form-control uf-attachment-count" id="attachment-count"
                               value="{% if voucher %}{{ voucher.attachment_count or 0 }}{% else %}0{% endif %}"
                               min="0" {% if mode == 'view' %}readonly{% endif %}>
                        <span class="uf-attachment-unit">张</span>
                    </div>
                </div>

                <!-- 制单人 -->
                <div class="uf-form-group">
                    <label class="uf-form-label">制单:</label>
                    <span class="uf-form-text">
                        {% if voucher and voucher.created_by %}{{ voucher.created_by.username }}{% else %}{{ current_user.username }}{% endif %}
                    </span>
                </div>
            </div>
        </div>

        <!-- 凭证表格 -->
        <div class="uf-voucher-table-container">
            <table class="uf-table uf-voucher-table" id="voucher-table">
                <thead>
                    <tr>
                        <th class="uf-col-sequence">序号</th>
                        <th class="uf-col-summary">摘要</th>
                        <th class="uf-col-subject">会计科目</th>
                        <th class="uf-col-debit">借方金额</th>
                        <th class="uf-col-credit">贷方金额</th>
                            
                        {% if mode != 'view' %}
                        <th class="uf-col-operations">操作</th>
                        {% endif %}
                    </tr>
                </thead>
                <tbody id="voucher-tbody">
                    {% if voucher and details %}
                        {% for detail in details %}
                        <tr class="uf-voucher-row" data-row="{{ loop.index }}" data-detail-id="{{ detail.id }}">
                            <td class="uf-line-number">{{ loop.index }}</td>
                            <td class="uf-summary-cell">
                                <textarea class="uf-form-control uf-summary-input"
                                          {% if mode == 'view' %}readonly{% endif %}
                                          placeholder="摘要"
                                          rows="2">{{ detail.summary }}</textarea>
                            </td>
                            <td class="uf-subject-cell">
                                <div class="uf-subject-selector">
                                    <input type="text" class="uf-form-control uf-subject-code"
                                           value="{{ detail.subject.code }}"
                                           {% if mode == 'view' %}readonly{% else %}readonly onclick="openSubjectModal(this)"{% endif %}
                                           placeholder="科目编码">
                                    <input type="text" class="uf-form-control uf-subject-name"
                                           value="{{ detail.subject.name }}"
                                           readonly placeholder="科目名称">
                                    <input type="hidden" class="uf-subject-id" value="{{ detail.subject.id }}">
                                </div>
                            </td>
                            <td class="uf-amount-cell">
                                <div class="uf-amount-input-container">
                                    <input type="text" class="uf-form-control uf-amount-input uf-debit-amount"
                                           value="{% if detail.debit_amount > 0 %}{{ '{:,.2f}'.format(detail.debit_amount) }}{% endif %}"
                                           {% if mode == 'view' %}readonly{% endif %}
                                           placeholder=""
                                           data-amount-type="debit">
                                </div>
                            </td>
                            <td class="uf-amount-cell">
                                <div class="uf-amount-input-container">
                                    <input type="text" class="uf-form-control uf-amount-input uf-credit-amount"
                                           value="{% if detail.credit_amount > 0 %}{{ '{:,.2f}'.format(detail.credit_amount) }}{% endif %}"
                                           {% if mode == 'view' %}readonly{% endif %}
                                           placeholder=""
                                           data-amount-type="credit">
                                </div>
                            </td>
                            {% if mode != 'view' %}
                            <td class="uf-operations-cell">
                                <button class="uf-btn uf-btn-sm" onclick="insertRowAbove(this)" title="插行">
                                    <i class="uf-icon">⬆️</i>
                                </button>
                                <button class="uf-btn uf-btn-sm" onclick="deleteRow(this)" title="删行">
                                    <i class="uf-icon">🗑️</i>
                                </button>
                            </td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                    {% else %}
                        <!-- 新建模式下的空行将通过JavaScript动态生成 -->
                    {% endif %}
                </tbody>
                <tfoot>
                    <!-- 大写金额行 -->
                    <tr class="uf-chinese-amount-row">
                        <td class="uf-chinese-amount-label">大写</td>
                        <td colspan="{% if mode != 'view' %}5{% else %}4{% endif %}" class="uf-chinese-amount-cell">
                            <div class="uf-chinese-amount-container">
                                <span class="uf-chinese-prefix">人民币(大写)：</span>
                                <span id="amount-chinese" class="uf-chinese-amount-text">零元整</span>
                            </div>
                        </td>
                    </tr>
                    <!-- 合计行 -->
                    <tr class="uf-totals-row">
                        <td class="uf-totals-label">合计</td>
                        <td class="uf-totals-label"></td>
                        <td class="uf-totals-label"></td>
                        <td class="uf-amount-cell uf-totals-amount">
                            <span class="uf-currency">￥</span>
                            <span id="debit-total">0.00</span>
                        </td>
                        <td class="uf-amount-cell uf-totals-amount">
                            <span class="uf-currency">￥</span>
                            <span id="credit-total">0.00</span>
                        </td>
                        {% if mode != 'view' %}
                        <td class="uf-operations-cell">
                            <div class="uf-balance-indicator">
                                <span id="balance-status" class="uf-balance-text">平衡</span>
                            </div>
                        </td>
                        {% endif %}
                    </tr>
                </tfoot>
            </table>
        </div>

        <!-- 签字区域 -->
        <div class="uf-signature-area">
            <div class="uf-signature-row">
                <div class="uf-signature-item">
                    <span class="uf-signature-label">制单:</span>
                    <span class="uf-signature-box">{% if voucher and voucher.created_by %}{{ voucher.created_by.username }}{% else %}{{ current_user.username }}{% endif %}</span>
                </div>
                <div class="uf-signature-item">
                    <span class="uf-signature-label">审核:</span>
                    <span class="uf-signature-box">{% if voucher and voucher.reviewed_by %}{{ voucher.reviewed_by.username }}{% endif %}</span>
                </div>
                <div class="uf-signature-item">
                    <span class="uf-signature-label">记账:</span>
                    <span class="uf-signature-box">{% if voucher and voucher.posted_by %}{{ voucher.posted_by.username }}{% endif %}</span>
                </div>
                <div class="uf-signature-item">
                    <span class="uf-signature-label">出纳:</span>
                    <span class="uf-signature-box"></span>
                </div>
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="uf-status-bar">
            <div class="uf-status-left">
                <span class="uf-status-text">就绪</span>
                <span class="uf-status-separator">|</span>
                <span class="uf-status-text" id="current-time"></span>
            </div>
            <div class="uf-status-right">
                <span class="uf-status-text">用友财务软件</span>
            </div>
        </div>
    </div>
</div>

<!-- 用友风格科目选择窗口 -->
<div class="uf-modal" id="subjectModal" style="display: none;">
    <div class="uf-modal-backdrop" onclick="closeSubjectModal()"></div>
    <div class="uf-modal-dialog">
        <div class="uf-modal-content">
            <!-- 模态框标题栏 -->
            <div class="uf-modal-header">
                <div class="uf-modal-title">
                    <i class="uf-icon">📁</i>
                    <span>会计科目选择</span>
                </div>
                <button class="uf-modal-close" onclick="closeSubjectModal()">
                    <i class="uf-icon">✕</i>
                </button>
            </div>

            <!-- 工具栏 -->
            <div class="uf-modal-toolbar">
                <div class="uf-toolbar-group">
                    <button class="uf-btn uf-btn-primary" onclick="confirmSubjectSelection()">
                        <i class="uf-icon">✓</i> 确定
                    </button>
                    <button class="uf-btn" onclick="closeSubjectModal()">
                        <i class="uf-icon">✕</i> 取消
                    </button>
                </div>
                <div class="uf-toolbar-separator"></div>
                <div class="uf-search-group">
                    <input type="text" class="uf-form-control uf-search-input" id="subject-search"
                           placeholder="输入科目编码或名称搜索...">
                    <button class="uf-btn" onclick="searchSubjects()">
                        <i class="uf-icon">🔍</i> 搜索
                    </button>
                </div>
            </div>

            <!-- 科目列表 -->
            <div class="uf-modal-body">
                <div class="uf-subject-list-container">
                    <table class="uf-table uf-subject-table" id="subjectTable">
                        <thead>
                            <tr>
                                <th class="uf-col-code">科目编码</th>
                                <th class="uf-col-name">科目名称</th>
                                <th class="uf-col-type">科目类型</th>
                                <th class="uf-col-level">级次</th>
                                <th class="uf-col-direction">余额方向</th>
                            </tr>
                        </thead>
                        <tbody id="subjectTableBody">
                            <!-- 科目数据将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 状态栏 -->
            <div class="uf-modal-footer">
                <div class="uf-status-info">
                    <span id="subjectCount">共 0 个科目</span>
                    <span class="uf-status-separator">|</span>
                    <span id="selectedSubjectInfo">未选择科目</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
// 全局变量
let currentRow = 0;
let currentCol = 0;
let subjects = [];
let selectedSubjectCell = null;
let voucherMode = '{{ mode|default("create") }}';
let voucherId = {% if voucher %}{{ voucher.id }}{% else %}null{% endif %};
let selectedSubject = null;

// 页面初始化
$(document).ready(function() {
    console.log('🚀 用友风格凭证编辑器初始化...');

    // 加载会计科目数据
    loadSubjects();

    // 初始化编辑器
    initUFVoucherEditor();

    // 更新时间显示
    updateTime();
    setInterval(updateTime, 1000);

    // 绑定事件
    bindUFEvents();

    // 根据模式初始化数据
    if (voucherMode === 'create' && $('#voucher-tbody tr').length === 0) {
        // 新建模式：添加默认6行
        for (let i = 0; i < 6; i++) {
            addUFRow();
        }
    } else if (voucherMode === 'view' || voucherMode === 'edit') {
        // 查看或编辑模式：更新合计
        updateUFTotals();
        checkUFBalance();
    }

    // 强制更新一次合计和大写金额
    setTimeout(() => {
        updateUFTotals();
        checkUFBalance();

        // 测试大写金额转换功能
        console.log('🧪 测试大写金额转换:');
        console.log('1234.56 =>', convertAmountToChinese(1234.56));
        console.log('0 =>', convertAmountToChinese(0));
        console.log('100 =>', convertAmountToChinese(100));
    }, 500);

    console.log('✅ 用友风格凭证编辑器初始化完成');
});

// 初始化用友风格凭证编辑器
function initUFVoucherEditor() {
    console.log('初始化用友风格凭证编辑器');

    // 设置今天日期
    const today = new Date().toISOString().split('T')[0];
    $('#voucher-date').val(today);

    // 初始化金额格式化
    initAmountFormatting();
}

// 绑定用友风格事件
function bindUFEvents() {
    // 键盘导航
    $(document).on('keydown', '.uf-form-control', function(e) {
        handleUFKeyNavigation(e, this);
    });

    // 用友风格金额输入事件
    $(document).on('input', '.uf-amount-input', function() {
        formatUFAmountInput(this);
        updateUFTotals();
        checkUFBalance();
    });

    $(document).on('blur', '.uf-amount-input', function() {
        finalizeUFAmountFormat(this);
        updateUFTotals();
        checkUFBalance();
    });

    $(document).on('focus', '.uf-amount-input', function() {
        prepareUFAmountEdit(this);
    });

    // 智能科目选择器事件
    $(document).on('focus', '.uf-subject-input', function() {
        if (voucherMode !== 'view') {
            showSubjectDropdown(this);
        }
    });

    $(document).on('input', '.uf-subject-input', function() {
        if (voucherMode !== 'view') {
            filterSubjects(this);
        }
    });

    $(document).on('keydown', '.uf-subject-input', function(e) {
        if (voucherMode !== 'view') {
            handleSubjectKeydown(e, this);
        }
    });

    $(document).on('click', '.uf-subject-option', function() {
        selectSubject(this);
    });

    // 点击其他地方隐藏下拉框
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.uf-subject-selector').length) {
            $('.uf-subject-dropdown').hide();
        }
    });

    // 科目搜索
    $('#subject-search').on('input', function() {
        searchUFSubjects();
    });

    $('#subject-search').on('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            searchUFSubjects();
        }
    });

    // 快捷键
    $(document).on('keydown', function(e) {
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            saveUFVoucher();
        }
        if (e.key === 'F9') {
            e.preventDefault();
            checkUFBalance();
        }
        if (e.key === 'Escape') {
            closeSubjectModal();
        }
    });

    // 科目表格行选择
    $(document).on('click', '.uf-subject-table tbody tr', function() {
        $('.uf-subject-table tbody tr').removeClass('selected');
        $(this).addClass('selected');

        const subjectId = $(this).data('subject-id');
        const subject = subjects.find(s => s.id === subjectId);
        if (subject) {
            selectedSubject = subject;
            updateSelectedSubjectInfo(subject);
        }
    });

    // 科目表格双击确认
    $(document).on('dblclick', '.uf-subject-table tbody tr', function() {
        confirmSubjectSelection();
    });
}

// 用友风格键盘导航
function handleUFKeyNavigation(e, element) {
    const $element = $(element);
    const $row = $element.closest('tr');
    const $cell = $element.closest('td');
    const rowIndex = $row.index();
    const cellIndex = $cell.index();

    switch(e.key) {
        case 'Tab':
            e.preventDefault();
            if (e.shiftKey) {
                moveUFToPreviousCell(rowIndex, cellIndex);
            } else {
                moveUFToNextCell(rowIndex, cellIndex);
            }
            break;
        case 'Enter':
            e.preventDefault();
            if ($element.hasClass('uf-subject-code')) {
                openSubjectModal(element);
            } else {
                moveUFToNextRow(rowIndex, cellIndex);
            }
            break;
        case 'ArrowUp':
            e.preventDefault();
            moveUFToPreviousRow(rowIndex, cellIndex);
            break;
        case 'ArrowDown':
            e.preventDefault();
            moveUFToNextRow(rowIndex, cellIndex);
            break;
        case 'F7':
            e.preventDefault();
            if ($element.hasClass('uf-subject-code')) {
                openSubjectModal(element);
            }
            break;
    }
}

// 用友风格单元格导航函数
function moveUFToNextCell(rowIndex, cellIndex) {
    const $tbody = $('#voucher-tbody');
    const $currentRow = $tbody.find('tr').eq(rowIndex);
    const maxCellIndex = voucherMode === 'view' ? 4 : 5;

    if (cellIndex < maxCellIndex) {
        const $nextCell = $currentRow.find('td').eq(cellIndex + 1).find('.uf-form-control').first();
        if ($nextCell.length) {
            $nextCell.focus().select();
        }
    } else {
        moveUFToNextRow(rowIndex, 1); // 移动到下一行的摘要列
    }
}

function moveUFToPreviousCell(rowIndex, cellIndex) {
    const $tbody = $('#voucher-tbody');
    const $currentRow = $tbody.find('tr').eq(rowIndex);

    if (cellIndex > 1) {
        const $prevCell = $currentRow.find('td').eq(cellIndex - 1).find('.uf-form-control').first();
        if ($prevCell.length) {
            $prevCell.focus().select();
        }
    } else if (rowIndex > 0) {
        const $prevRow = $tbody.find('tr').eq(rowIndex - 1);
        const maxCellIndex = voucherMode === 'view' ? 4 : 5;
        const $lastCell = $prevRow.find('td').eq(maxCellIndex).find('.uf-form-control').first();
        if ($lastCell.length) {
            $lastCell.focus().select();
        }
    }
}

function moveUFToNextRow(rowIndex, cellIndex) {
    const $tbody = $('#voucher-tbody');
    let $nextRow = $tbody.find('tr').eq(rowIndex + 1);

    if ($nextRow.length === 0 && voucherMode !== 'view') {
        // 如果没有下一行且不是查看模式，自动添加新行
        addUFRow();
        $nextRow = $tbody.find('tr').last();
    }

    if ($nextRow.length > 0) {
        const targetCellIndex = Math.max(1, cellIndex); // 至少从摘要列开始
        const $targetCell = $nextRow.find('td').eq(targetCellIndex).find('.uf-form-control').first();
        if ($targetCell.length) {
            $targetCell.focus().select();
        }
    }
}

function moveUFToPreviousRow(rowIndex, cellIndex) {
    if (rowIndex > 0) {
        const $tbody = $('#voucher-tbody');
        const $prevRow = $tbody.find('tr').eq(rowIndex - 1);
        const $targetCell = $prevRow.find('td').eq(cellIndex).find('.uf-form-control').first();
        if ($targetCell.length) {
            $targetCell.focus().select();
        }
    }
}

// 用友风格添加行函数
function addRow() {
    addUFRow();
}

function addUFRow() {
    if (voucherMode === 'view') {
        return; // 查看模式不允许添加行
    }

    const $tbody = $('#voucher-tbody');
    const rowCount = $tbody.find('tr').length + 1;

    const operationsColumn = voucherMode !== 'view' ? `
        <td class="uf-operations-cell">
            <button class="uf-btn uf-btn-sm" onclick="insertRowAbove(this)" title="插行">
                <i class="uf-icon">⬆️</i>
            </button>
            <button class="uf-btn uf-btn-sm" onclick="deleteUFRow(this)" title="删行">
                <i class="uf-icon">🗑️</i>
            </button>
        </td>
    ` : '';

    const newRow = `
        <tr class="uf-voucher-row" data-row="${rowCount}">
            <td class="uf-line-number">${rowCount}</td>
            <td class="uf-summary-cell">
                <textarea class="uf-form-control uf-summary-input" placeholder="摘要" rows="2"></textarea>
            </td>
            <td class="uf-subject-cell">
                <div class="uf-subject-selector">
                    <div class="uf-subject-input-container">
                        <input type="text" class="uf-form-control uf-subject-input"
                               placeholder="输入科目编码/名称/拼音首字母"
                               autocomplete="off">
                        <div class="uf-subject-dropdown" style="display: none;">
                            <div class="uf-subject-dropdown-list">
                                <!-- 科目选项将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                    <input type="hidden" class="uf-subject-id">
                    <input type="hidden" class="uf-subject-code">
                    <input type="hidden" class="uf-subject-name">
                </div>
            </td>
            <td class="uf-amount-cell">
                <div class="uf-amount-input-container">
                    <input type="text" class="uf-form-control uf-amount-input uf-debit-amount"
                           placeholder="" data-amount-type="debit">
                </div>
            </td>
            <td class="uf-amount-cell">
                <div class="uf-amount-input-container">
                    <input type="text" class="uf-form-control uf-amount-input uf-credit-amount"
                           placeholder="" data-amount-type="credit">
                </div>
            </td>
            ${operationsColumn}
        </tr>
    `;

    $tbody.append(newRow);
    updateUFLineNumbers();

    // 聚焦到新行的摘要输入框
    const $newRow = $tbody.find('tr').last();
    const $summaryInput = $newRow.find('.uf-summary-input');

    // 初始化新添加的摘要输入框的自动调整功能
    setTimeout(() => {
        autoResizeTextarea($summaryInput[0]);
        $summaryInput.focus();
    }, 100);
}

// 用友风格删除行函数
function deleteRow() {
    deleteUFRow();
}

function deleteUFRow(button) {
    if (voucherMode === 'view') {
        return; // 查看模式不允许删除行
    }

    const $tbody = $('#voucher-tbody');
    const $rows = $tbody.find('tr');

    if (button) {
        // 删除指定行
        const $row = $(button).closest('tr');
        if ($rows.length > 1) {
            $row.remove();
            updateUFLineNumbers();
            updateUFTotals();
            checkUFBalance();
        }
    } else {
        // 删除最后一行
        if ($rows.length > 1) {
            $rows.last().remove();
            updateUFLineNumbers();
            updateUFTotals();
            checkUFBalance();
        }
    }
}

function insertRow() {
    // 在当前焦点行之后插入新行
    const $focusedInput = $('.cell-input:focus');
    if ($focusedInput.length) {
        const $currentRow = $focusedInput.closest('tr');
        const rowNumber = $currentRow.index() + 1;

        const rowHtml = `
            <tr data-row="${rowNumber}">
                <td class="line-number">${rowNumber}</td>
                <td>
                    <textarea class="cell-input summary-input" placeholder="摘要" rows="2"></textarea>
                </td>
                <td class="subject-cell">
                    <div class="subject-selector">
                        <input type="text" class="cell-input subject-code" placeholder="科目" readonly onclick="openSubjectModal(this)">
                        <input type="text" class="cell-input subject-name" placeholder="科目名称" readonly>
                        <input type="hidden" class="subject-id">
                    </div>
                </td>
                <td>
                    <input type="number" class="cell-input amount-input debit-amount"
                           step="0.01" min="0" placeholder="0.00">
                </td>
                <td>
                    <input type="number" class="cell-input amount-input credit-amount"
                           step="0.01" min="0" placeholder="0.00">
                </td>
            </tr>
        `;

        $currentRow.after(rowHtml);
        updateRowNumbers();
    } else {
        addRow();
    }
}

function copyRow() {
    const $focusedInput = $('.cell-input:focus');
    if ($focusedInput.length) {
        const $currentRow = $focusedInput.closest('tr');
        const $newRow = $currentRow.clone();

        // 清空新行的序号和ID
        $newRow.find('.line-number').text('');
        $newRow.removeAttr('data-row');

        $currentRow.after($newRow);
        updateRowNumbers();
    }
}

// 用友风格行号更新
function updateRowNumbers() {
    updateUFLineNumbers();
}

function updateUFLineNumbers() {
    $('#voucher-tbody tr').each(function(index) {
        $(this).find('.uf-line-number').text(index + 1);
        $(this).attr('data-row', index + 1);
    });
}

// 用友风格合计更新
function updateTotals() {
    updateUFTotals();
}

function updateUFTotals() {
    let debitTotal = 0;
    let creditTotal = 0;

    $('.uf-debit-amount').each(function() {
        const value = parseUFAmount($(this).val());
        debitTotal += value;
    });

    $('.uf-credit-amount').each(function() {
        const value = parseUFAmount($(this).val());
        creditTotal += value;
    });

    // 用友风格合计金额显示
    $('#debit-total').text(formatUFAmountDisplay(debitTotal));
    $('#credit-total').text(formatUFAmountDisplay(creditTotal));

    // 更新大写金额 - 财务凭证中通常显示借方或贷方的合计金额
    // 如果借贷平衡，显示借方金额；如果不平衡，显示较大的一方
    let displayAmount = 0;
    if (Math.abs(debitTotal - creditTotal) < 0.01) {
        // 借贷平衡，显示借方金额
        displayAmount = debitTotal;
    } else {
        // 不平衡，显示较大的一方
        displayAmount = Math.max(debitTotal, creditTotal);
    }

    const chineseAmount = convertAmountToChinese(displayAmount);
    $('#amount-chinese').text(chineseAmount);

    console.log(`📊 合计更新: 借方 ${debitTotal}, 贷方 ${creditTotal}, 大写: ${chineseAmount}`);
}

// 用友风格平衡检查
function checkBalance() {
    checkUFBalance();
}

function checkUFBalance() {
    const debitText = $('#debit-total').text().replace(/[¥,]/g, '');
    const creditText = $('#credit-total').text().replace(/[¥,]/g, '');
    const debitTotal = parseFloat(debitText) || 0;
    const creditTotal = parseFloat(creditText) || 0;
    const difference = Math.abs(debitTotal - creditTotal);

    const $indicator = $('#balance-indicator');

    if (difference < 0.01) {
        $indicator.removeClass('unbalanced unchecked').addClass('balanced').text('借贷平衡');
    } else {
        $indicator.removeClass('balanced unchecked').addClass('unbalanced').text(`不平衡 差额:¥${difference.toFixed(2)}`);
    }
}

// 用友风格金额格式化函数
function formatUFAmount(amount) {
    if (!amount || amount === 0) return '0.00';
    return parseFloat(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

function parseUFAmount(amountStr) {
    if (!amountStr) return 0;
    // 移除千分位分隔符和货币符号
    const cleanStr = amountStr.toString().replace(/[¥,]/g, '');
    const amount = parseFloat(cleanStr);
    return isNaN(amount) ? 0 : amount;
}

// 用友风格金额输入格式化
function formatUFAmount(input) {
    const value = parseUFAmount($(input).val());
    if (value > 0) {
        $(input).val(formatUFAmount(value));
    }
}

// 初始化金额格式化
function initAmountFormatting() {
    $(document).on('blur', '.uf-amount-input', function() {
        formatUFAmount(this);
    });
}

function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString();
    $('#current-time').text(timeString);
}

function loadSubjects() {
    console.log('开始加载会计科目数据...');

    $.ajax({
        url: '{{ url_for("financial.accounting_subjects_api") }}',
        type: 'GET',
        dataType: 'json',
        timeout: 10000,
        success: function(response) {
            console.log('API响应:', response);

            // 适配现有API的返回格式（直接返回数组）
            if (Array.isArray(response) && response.length > 0) {
                subjects = response;
                console.log(`✅ 科目数据加载成功: ${subjects.length} 个科目`);

                // 按科目类型分组显示统计，并添加拼音首字母
                const typeStats = {};
                subjects.forEach(subject => {
                    const type = subject.subject_type || '其他';
                    typeStats[type] = (typeStats[type] || 0) + 1;

                    // 添加拼音首字母
                    subject.pinyin = getSubjectPinyin(subject.name);
                });
                console.log('科目类型统计:', typeStats);

                // 显示部分科目数据用于调试
                if (subjects.length > 0) {
                    console.log('前5个科目示例:', subjects.slice(0, 5));
                }

                // 构建科目树并渲染
                buildSubjectTree();
                renderSubjectTree();
                updateSubjectStatusBar();

            } else if (response && response.success === false) {
                console.error('❌ 加载科目失败:', response.message || '未知错误');
                subjects = [];
                alert('加载会计科目失败: ' + (response.message || '请检查网络连接'));
            } else {
                console.warn('⚠️ 未获取到科目数据，可能是空数组');
                subjects = [];
                alert('未获取到会计科目数据，请检查数据库中是否有科目数据');
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ AJAX请求失败:');
            console.error('状态:', status);
            console.error('错误:', error);
            console.error('响应文本:', xhr.responseText);

            subjects = [];
            alert('网络错误，无法加载会计科目: ' + error);
        }
    });
}

// 用友风格科目选择器相关变量（已在顶部声明）
let subjectTree = [];
let filteredSubjects = [];

// 用友风格科目选择器
function openSubjectModal(element) {
    selectedSubjectCell = $(element).closest('.uf-subject-selector');

    // 显示用友风格科目选择器
    $('#subjectModal').show();

    // 构建科目表格
    buildUFSubjectTable();

    // 聚焦搜索框
    $('#subject-search').focus();
}

function closeSubjectModal() {
    $('#subjectModal').hide();
    selectedSubject = null;
    selectedSubjectCell = null;
}

function buildUFSubjectTable() {
    console.log('构建用友风格科目表格，科目数量:', subjects.length);

    if (!subjects || subjects.length === 0) {
        console.warn('⚠️ 没有科目数据，无法构建科目表格');
        return;
    }

    const $tableBody = $('#subjectTableBody');
    $tableBody.empty();

    // 按科目编码排序
    const sortedSubjects = [...subjects].sort((a, b) => a.code.localeCompare(b.code));

    sortedSubjects.forEach(subject => {
        const level = subject.level || getSubjectLevel(subject.code);
        const balanceDirection = subject.balance_direction || getBalanceDirection(subject.subject_type);
        const sourceText = subject.is_system ? '系统' : '学校';

        const rowHtml = `
            <tr data-subject-id="${subject.id}" data-subject-code="${subject.code}">
                <td class="uf-col-code">${subject.code}</td>
                <td class="uf-col-name" style="padding-left: ${(level - 1) * 20}px;">
                    ${level > 1 ? '└ ' : ''}${subject.name}
                </td>
                <td class="uf-col-type">${subject.subject_type || '-'}</td>
                <td class="uf-col-level">${level}</td>
                <td class="uf-col-direction">${balanceDirection}</td>
            </tr>
        `;

        $tableBody.append(rowHtml);
    });

    // 更新状态信息
    $('#subjectCount').text(`共 ${subjects.length} 个科目`);
    $('#selectedSubjectInfo').text('未选择科目');
}

function buildSubjectTree() {
    console.log('构建用友风格科目树，科目数量:', subjects.length);

    if (!subjects || subjects.length === 0) {
        console.warn('⚠️ 没有科目数据，无法构建科目树');
        subjectTree = [];
        return;
    }

    // 按科目编码排序
    const sortedSubjects = [...subjects].sort((a, b) => a.code.localeCompare(b.code));

    // 构建用友风格树形结构
    subjectTree = [];
    const subjectMap = new Map();

    // 按科目类型分组
    const typeGroups = {
        '资产': [],
        '负债': [],
        '所有者权益': [],
        '收入': [],
        '费用': [],
        '成本': [],
        '其他': []
    };

    sortedSubjects.forEach(subject => {
        const level = subject.level || getSubjectLevel(subject.code);
        const parentCode = getParentSubjectCode(subject.code);
        const subjectType = subject.subject_type || '其他';

        const treeNode = {
            ...subject,
            level: level,
            children: [],
            expanded: level <= 2, // 默认展开前两级
            parent: parentCode,
            isSystemSubject: subject.is_system,
            displayText: `${subject.code} ${subject.name}`,
            typeGroup: subjectType,
            display_name: `${subject.code} ${subject.name}`,
            balance_direction: subject.balance_direction || getBalanceDirection(subject.subject_type)
        };

        subjectMap.set(subject.code, treeNode);

        // 按类型分组
        if (typeGroups[subjectType]) {
            typeGroups[subjectType].push(treeNode);
        } else {
            typeGroups['其他'].push(treeNode);
        }
    });

    // 构建层级关系
    subjectMap.forEach(node => {
        if (node.parent && subjectMap.has(node.parent)) {
            subjectMap.get(node.parent).children.push(node);
        } else {
            // 顶级科目
            subjectTree.push(node);
        }
    });

    // 按科目类型重新组织（用友风格）
    const organizedTree = [];
    Object.keys(typeGroups).forEach(type => {
        if (typeGroups[type].length > 0) {
            // 创建类型分组节点
            const typeNode = {
                id: `type_${type}`,
                code: '',
                name: `${type}类科目`,
                level: 0,
                children: typeGroups[type].filter(node => node.level === 1),
                expanded: true,
                isTypeGroup: true,
                typeGroup: type
            };
            organizedTree.push(typeNode);
        }
    });

    subjectTree = organizedTree;
    console.log('✅ 科目树构建完成，顶级节点数:', subjectTree.length);
}

function getSubjectLevel(code) {
    // 根据科目编码长度判断级次
    if (code.length <= 4) return 1;
    if (code.length <= 6) return 2;
    if (code.length <= 8) return 3;
    return 4;
}

function getParentSubjectCode(code) {
    if (code.length <= 4) return null;
    if (code.length <= 6) return code.substring(0, 4);
    if (code.length <= 8) return code.substring(0, 6);
    return code.substring(0, 8);
}

function renderSubjectTree() {
    const $treeContent = $('#subjectTree');
    $treeContent.empty();

    subjectTree.forEach(node => {
        renderTreeNode(node, $treeContent, 0);
    });
}

function renderTreeNode(node, container, depth) {
    const indent = depth * 20;
    const hasChildren = node.children && node.children.length > 0;
    const expandIcon = hasChildren ? (node.expanded ? '▼' : '▶') : '　';

    // 用友风格：区分类型分组和科目节点
    let nodeClass = 'uf-tree-node';
    let iconClass = '📁';
    let textStyle = '';

    if (node.isTypeGroup) {
        // 科目类型分组
        nodeClass += ' uf-type-group';
        iconClass = '📂';
        textStyle = 'font-weight: bold; color: #1890ff;';
    } else if (node.isSystemSubject) {
        // 系统科目
        nodeClass += ' uf-system-subject';
        iconClass = '🏛️';
        textStyle = 'color: #52c41a;';
    } else {
        // 学校自定义科目
        nodeClass += ' uf-school-subject';
        iconClass = '🏫';
        textStyle = 'color: #722ed1;';
    }

    const nodeId = node.isTypeGroup ? node.id : node.id;
    const nodeCode = node.isTypeGroup ? '' : node.code;
    const displayText = node.isTypeGroup ? node.name : `${node.code} ${node.name}`;

    const nodeHtml = `
        <div class="${nodeClass}"
             data-subject-id="${nodeId}"
             data-subject-code="${nodeCode}"
             data-level="${node.level}"
             style="padding-left: ${indent}px;">
            <span class="uf-tree-expand" onclick="toggleTreeNode('${nodeCode || nodeId}')">${expandIcon}</span>
            <span class="uf-tree-icon">${iconClass}</span>
            <span class="uf-tree-text"
                  onclick="${node.isTypeGroup ? '' : `selectTreeSubject('${nodeCode}')`}"
                  style="${textStyle}"
                  title="${node.isTypeGroup ? '科目分组' : (node.isSystemSubject ? '系统科目' : '学校科目')}">
                ${displayText}
            </span>
        </div>
    `;

    container.append(nodeHtml);

    if (hasChildren && node.expanded) {
        node.children.forEach(child => {
            renderTreeNode(child, container, depth + 1);
        });
    }
}

function toggleTreeNode(code) {
    const node = findNodeByCode(subjectTree, code);
    if (node && node.children.length > 0) {
        node.expanded = !node.expanded;
        renderSubjectTree();
    }
}

function findNodeByCode(nodes, code) {
    for (let node of nodes) {
        if (node.code === code) return node;
        if (node.children) {
            const found = findNodeByCode(node.children, code);
            if (found) return found;
        }
    }
    return null;
}

function selectTreeSubject(code) {
    console.log('选择科目:', code);

    const subject = subjects.find(s => s.code === code);
    if (subject) {
        selectedSubject = subject;
        updateSubjectDetails(subject);

        // 高亮选中的节点
        $('.uf-tree-node').removeClass('selected');
        $(`.uf-tree-node[data-subject-code="${code}"]`).addClass('selected');

        console.log('✅ 已选择科目:', subject.display_name || `${subject.code} ${subject.name}`);
    } else {
        console.warn('⚠️ 未找到科目:', code);
    }
}

function updateSubjectDetails(subject) {
    $('#selectedSubjectCode').text(subject.code || '-');
    $('#selectedSubjectName').text(subject.name || '-');
    $('#selectedSubjectType').text(subject.subject_type || '-');
    $('#selectedSubjectLevel').text(subject.level || getSubjectLevel(subject.code));
    $('#selectedSubjectDirection').text(subject.balance_direction || getBalanceDirection(subject.subject_type));

    // 显示科目来源
    const sourceText = subject.is_system ? '系统科目' : '学校科目';
    const infoText = `已选择: ${subject.code} ${subject.name} (${sourceText})`;
    $('#selectedSubjectInfo').text(infoText);

    console.log('更新科目详情:', {
        code: subject.code,
        name: subject.name,
        type: subject.subject_type,
        level: subject.level,
        direction: subject.balance_direction,
        isSystem: subject.is_system
    });
}

function getBalanceDirection(subjectType) {
    const directions = {
        '资产': '借方',
        '负债': '贷方',
        '所有者权益': '贷方',
        '收入': '贷方',
        '费用': '借方',
        '成本': '借方'
    };
    return directions[subjectType] || '-';
}

function expandAllSubjects() {
    expandCollapseAll(subjectTree, true);
    renderSubjectTree();
}

function collapseAllSubjects() {
    expandCollapseAll(subjectTree, false);
    renderSubjectTree();
}

function expandCollapseAll(nodes, expand) {
    nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
            node.expanded = expand;
            expandCollapseAll(node.children, expand);
        }
    });
}

// 用友风格科目选择确认
function confirmSubjectSelection() {
    if (!selectedSubject || !selectedSubjectCell) {
        alert('请先选择一个科目');
        return;
    }

    // 填充科目信息到表格
    selectedSubjectCell.find('.uf-subject-code').val(selectedSubject.code);
    selectedSubjectCell.find('.uf-subject-name').val(selectedSubject.name);
    selectedSubjectCell.find('.uf-subject-id').val(selectedSubject.id);

    console.log('✅ 科目选择完成:', selectedSubject.code, selectedSubject.name);

    // 关闭模态框
    closeSubjectModal();

    // 移动到下一个输入框（借方金额）
    const $row = selectedSubjectCell.closest('tr');
    const $debitInput = $row.find('.uf-debit-amount');
    setTimeout(() => {
        $debitInput.focus().select();
    }, 100);
}

function updateSelectedSubjectInfo(subject) {
    const infoText = `已选择: ${subject.code} ${subject.name}`;
    $('#selectedSubjectInfo').text(infoText);
}

// 用友风格科目搜索
function searchSubjects() {
    searchUFSubjects();
}

function searchUFSubjects() {
    const searchTerm = $('#subject-search').val().trim();
    console.log('搜索科目:', searchTerm);

    if (!searchTerm) {
        console.log('清空搜索，恢复科目表格');
        buildUFSubjectTable();
        updateSubjectStatusBar();
        return;
    }

    // 用友风格搜索：支持编码、名称、拼音首字母
    filteredSubjects = subjects.filter(subject => {
        const code = (subject.code || '').toLowerCase();
        const name = (subject.name || '').toLowerCase();
        const type = (subject.subject_type || '').toLowerCase();
        const search = searchTerm.toLowerCase();

        return code.includes(search) ||
               name.includes(search) ||
               type.includes(search) ||
               (subject.display_name || '').toLowerCase().includes(search);
    });

    console.log(`搜索到 ${filteredSubjects.length} 个匹配的科目`);

    // 渲染搜索结果
    renderSearchResults();
}

function renderSearchResults() {
    const $treeContent = $('#subjectTree');
    $treeContent.empty();

    if (filteredSubjects.length === 0) {
        $treeContent.append('<div class="uf-no-results">未找到匹配的科目</div>');
        updateSubjectStatusBar();
        return;
    }

    // 按科目类型分组显示搜索结果
    const typeGroups = {};
    filteredSubjects.forEach(subject => {
        const type = subject.subject_type || '其他';
        if (!typeGroups[type]) {
            typeGroups[type] = [];
        }
        typeGroups[type].push(subject);
    });

    Object.keys(typeGroups).forEach(type => {
        // 类型分组标题
        const typeHeaderHtml = `
            <div class="uf-search-type-header">
                <span class="uf-tree-icon">📂</span>
                <span style="font-weight: bold; color: #1890ff;">${type}类 (${typeGroups[type].length})</span>
            </div>
        `;
        $treeContent.append(typeHeaderHtml);

        // 该类型下的科目
        typeGroups[type].forEach(subject => {
            const iconClass = subject.is_system ? '🏛️' : '🏫';
            const sourceText = subject.is_system ? '系统' : '学校';
            const nodeHtml = `
                <div class="uf-tree-node search-result"
                     data-subject-id="${subject.id}"
                     data-subject-code="${subject.code}"
                     style="padding-left: 20px;">
                    <span class="uf-tree-icon">${iconClass}</span>
                    <span class="uf-tree-text" onclick="selectTreeSubject('${subject.code}')">
                        ${subject.code} ${subject.name}
                        <small style="color: #999; margin-left: 8px;">[${sourceText}]</small>
                    </span>
                </div>
            `;
            $treeContent.append(nodeHtml);
        });
    });

    updateSubjectStatusBar();
}

function updateSubjectStatusBar() {
    const totalCount = subjects.length;
    const displayCount = filteredSubjects.length || totalCount;
    $('#subjectCount').text(`共 ${displayCount} 个科目`);
}

function confirmSubjectSelection() {
    if (selectedSubject && selectedSubjectCell) {
        selectedSubjectCell.find('.subject-code').val(selectedSubject.code);
        selectedSubjectCell.find('.subject-name').val(selectedSubject.name);
        selectedSubjectCell.find('.subject-id').val(selectedSubject.id);

        closeSubjectSelector();

        // 移动到下一个输入框
        const $nextInput = selectedSubjectCell.closest('td').next().find('.cell-input');
        if ($nextInput.length) {
            $nextInput.focus().select();
        }
    }
}

function closeSubjectSelector() {
    $('#subjectSelector').hide();
    selectedSubject = null;
    filteredSubjects = [];
    $('#uf-subject-search').val('');
}

function minimizeSubjectSelector() {
    // 最小化功能（可选实现）
    $('#subjectSelector .uf-content').toggle();
}

function saveVoucher() {
    const voucherData = collectVoucherData();

    if (!validateVoucherData(voucherData)) {
        return;
    }

    const url = voucherMode === 'edit' ?
        '{{ url_for("financial.update_voucher_api", id=999999) }}'.replace('999999', voucherId) :
        '{{ url_for("financial.create_voucher") }}';
    const method = voucherMode === 'edit' ? 'PUT' : 'POST';

    $.ajax({
        url: url,
        type: method,
        contentType: 'application/json',
        data: JSON.stringify(voucherData),
        success: function(response) {
            if (response.success) {
                alert('凭证保存成功！');
                const targetId = response.voucher_id || voucherId;
                window.location.href = '{{ url_for("financial.view_voucher", id=999999) }}'.replace('999999', targetId);
            } else {
                alert('保存失败：' + response.message);
            }
        },
        error: function(xhr, status, error) {
            alert('保存失败，请重试');
            console.error('保存失败:', error);
        }
    });
}

function collectVoucherData() {
    const details = [];

    $('#voucher-tbody tr').each(function() {
        const $row = $(this);
        const summary = $row.find('.uf-summary-input').val();
        const subjectId = $row.find('.uf-subject-id').val();
        const debitAmount = parseUFAmount($row.find('.uf-debit-amount').val()) || 0;
        const creditAmount = parseUFAmount($row.find('.uf-credit-amount').val()) || 0;

        if (summary || subjectId || debitAmount > 0 || creditAmount > 0) {
            details.push({
                summary: summary,
                subject_id: subjectId,
                debit_amount: debitAmount,
                credit_amount: creditAmount
            });
        }
    });

    const data = {
        voucher_type: $('#voucher-type').val(),
        voucher_date: $('#voucher-date').val(),
        attachment_count: parseInt($('#attachment-count').val()) || 0,
        details: details
    };

    // 在编辑模式下包含凭证号
    if (voucherMode === 'edit') {
        const voucherNumber = $('#voucher-number').val();
        if (voucherNumber && voucherNumber !== '自动') {
            // 直接使用用户输入的凭证号
            data.voucher_number = voucherNumber;
        }
    }

    return data;
}

function validateVoucherData(data) {
    if (data.details.length === 0) {
        alert('请至少添加一行凭证明细');
        return false;
    }

    let debitTotal = 0;
    let creditTotal = 0;

    for (let detail of data.details) {
        if (!detail.subject_id) {
            alert('请为所有明细行选择会计科目');
            return false;
        }

        if (detail.debit_amount === 0 && detail.credit_amount === 0) {
            alert('每行明细必须有借方或贷方金额');
            return false;
        }

        if (detail.debit_amount > 0 && detail.credit_amount > 0) {
            alert('每行明细不能同时有借方和贷方金额');
            return false;
        }

        debitTotal += detail.debit_amount;
        creditTotal += detail.credit_amount;
    }

    if (Math.abs(debitTotal - creditTotal) > 0.01) {
        alert('借贷不平衡，请检查金额');
        return false;
    }

    return true;
}

// ===== 凭证导航功能 =====

// 导航到首张凭证
function navigateToFirst() {
    if (voucherMode === 'create') {
        showUFMessage('新建模式下无法导航', 'warning');
        return;
    }

    $.ajax({
        url: '/financial/vouchers/navigate/first',
        method: 'GET',
        success: function(response) {
            if (response.success && response.voucher_id) {
                window.location.href = `{{ url_for('financial.view_voucher', id=999999) }}`.replace('999999', response.voucher_id);
            } else {
                showUFMessage(response.message || '没有找到凭证', 'warning');
            }
        },
        error: function() {
            showUFMessage('导航失败，请重试', 'error');
        }
    });
}

// 导航到上一张凭证
function navigateToPrevious() {
    if (voucherMode === 'create') {
        showUFMessage('新建模式下无法导航', 'warning');
        return;
    }

    if (!voucherId) {
        showUFMessage('当前凭证ID无效', 'error');
        return;
    }

    $.ajax({
        url: `/financial/vouchers/${voucherId}/navigate/previous`,
        method: 'GET',
        success: function(response) {
            if (response.success && response.voucher_id) {
                window.location.href = `{{ url_for('financial.view_voucher', id=999999) }}`.replace('999999', response.voucher_id);
            } else {
                showUFMessage(response.message || '已经是第一张凭证', 'info');
            }
        },
        error: function() {
            showUFMessage('导航失败，请重试', 'error');
        }
    });
}

// 导航到下一张凭证
function navigateToNext() {
    if (voucherMode === 'create') {
        showUFMessage('新建模式下无法导航', 'warning');
        return;
    }

    if (!voucherId) {
        showUFMessage('当前凭证ID无效', 'error');
        return;
    }

    $.ajax({
        url: `/financial/vouchers/${voucherId}/navigate/next`,
        method: 'GET',
        success: function(response) {
            if (response.success && response.voucher_id) {
                window.location.href = `{{ url_for('financial.view_voucher', id=999999) }}`.replace('999999', response.voucher_id);
            } else {
                showUFMessage(response.message || '已经是最后一张凭证', 'info');
            }
        },
        error: function() {
            showUFMessage('导航失败，请重试', 'error');
        }
    });
}

// 导航到末张凭证
function navigateToLast() {
    if (voucherMode === 'create') {
        showUFMessage('新建模式下无法导航', 'warning');
        return;
    }

    $.ajax({
        url: '/financial/vouchers/navigate/last',
        method: 'GET',
        success: function(response) {
            if (response.success && response.voucher_id) {
                window.location.href = `{{ url_for('financial.view_voucher', id=999999) }}`.replace('999999', response.voucher_id);
            } else {
                showUFMessage(response.message || '没有找到凭证', 'warning');
            }
        },
        error: function() {
            showUFMessage('导航失败，请重试', 'error');
        }
    });
}

// 跳转到指定凭证
function navigateToVoucher() {
    const voucherNumber = $('#nav-voucher-number').val().trim();
    if (!voucherNumber) {
        showUFMessage('请输入凭证号', 'warning');
        $('#nav-voucher-number').focus();
        return;
    }

    $.ajax({
        url: '/financial/vouchers/navigate/by-number',
        method: 'GET',
        data: { voucher_number: voucherNumber },
        success: function(response) {
            if (response.success && response.voucher_id) {
                window.location.href = `{{ url_for('financial.view_voucher', id=999999) }}`.replace('999999', response.voucher_id);
            } else {
                showUFMessage(response.message || '未找到指定凭证', 'warning');
                $('#nav-voucher-number').focus().select();
            }
        },
        error: function() {
            showUFMessage('查找凭证失败，请重试', 'error');
        }
    });
}

// ===== 打印功能 =====

// 打印凭证
function printVoucher() {
    if (voucherMode === 'create') {
        showUFMessage('请先保存凭证再打印', 'warning');
        return;
    }

    if (!voucherId) {
        showUFMessage('当前凭证ID无效', 'error');
        return;
    }

    // 打开打印页面
    const printUrl = `{{ url_for('financial.print_voucher', voucher_id=999999) }}`.replace('999999', voucherId);
    window.open(printUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
}

// 打印预览
function printPreview() {
    if (voucherMode === 'create') {
        showUFMessage('请先保存凭证再预览', 'warning');
        return;
    }

    if (!voucherId) {
        showUFMessage('当前凭证ID无效', 'error');
        return;
    }

    // 打开预览页面
    const previewUrl = `{{ url_for('financial.print_voucher', voucher_id=999999) }}`.replace('999999', voucherId) + '?preview=1';
    window.open(previewUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
}

// ===== 凭证操作功能 =====

// 新增凭证
function newVoucher() {
    if (voucherMode === 'edit' && hasUnsavedChanges()) {
        if (!confirm('当前凭证有未保存的修改，确定要新建凭证吗？')) {
            return;
        }
    }

    window.location.href = '{{ url_for("financial.create_voucher") }}';
}

// 复制凭证
function copyVoucher() {
    if (!voucherId) {
        showUFMessage('当前凭证ID无效', 'error');
        return;
    }

    if (!confirm('确定要复制当前凭证吗？')) {
        return;
    }

    $.ajax({
        url: `/financial/vouchers/${voucherId}/copy`,
        method: 'POST',
        headers: {
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        },
        success: function(response) {
            if (response.success && response.voucher_id) {
                showUFMessage('凭证复制成功', 'success');
                // 跳转到新复制的凭证编辑页面
                setTimeout(() => {
                    window.location.href = `{{ url_for('financial.edit_voucher_unified', id=999999) }}`.replace('999999', response.voucher_id);
                }, 1000);
            } else {
                showUFMessage(response.message || '复制凭证失败', 'error');
            }
        },
        error: function() {
            showUFMessage('复制凭证失败，请重试', 'error');
        }
    });
}

// 冲销凭证
function reverseVoucher() {
    if (!voucherId) {
        showUFMessage('当前凭证ID无效', 'error');
        return;
    }

    if (!confirm('确定要冲销当前凭证吗？冲销后将生成一张相反的凭证。')) {
        return;
    }

    $.ajax({
        url: `/financial/vouchers/${voucherId}/reverse`,
        method: 'POST',
        headers: {
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        },
        success: function(response) {
            if (response.success && response.voucher_id) {
                showUFMessage('凭证冲销成功', 'success');
                // 跳转到冲销凭证页面
                setTimeout(() => {
                    window.location.href = `{{ url_for('financial.view_voucher', id=999999) }}`.replace('999999', response.voucher_id);
                }, 1000);
            } else {
                showUFMessage(response.message || '冲销凭证失败', 'error');
            }
        },
        error: function() {
            showUFMessage('冲销凭证失败，请重试', 'error');
        }
    });
}

// 审核凭证
function reviewVoucher() {
    if (!voucherId) {
        showUFMessage('当前凭证ID无效', 'error');
        return;
    }

    if (!confirm('确定要审核当前凭证吗？')) {
        return;
    }

    $.ajax({
        url: `/financial/vouchers/${voucherId}/review`,
        method: 'POST',
        headers: {
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                showUFMessage('凭证审核成功', 'success');
                // 刷新页面显示最新状态
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showUFMessage(response.message || '审核凭证失败', 'error');
            }
        },
        error: function() {
            showUFMessage('审核凭证失败，请重试', 'error');
        }
    });
}

// 取消审核
function unReviewVoucher() {
    if (!voucherId) {
        showUFMessage('当前凭证ID无效', 'error');
        return;
    }

    if (!confirm('确定要取消审核当前凭证吗？')) {
        return;
    }

    $.ajax({
        url: `/financial/vouchers/${voucherId}/cancel-review`,
        method: 'POST',
        headers: {
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                showUFMessage('取消审核成功', 'success');
                // 刷新页面显示最新状态
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showUFMessage(response.message || '取消审核失败', 'error');
            }
        },
        error: function() {
            showUFMessage('取消审核失败，请重试', 'error');
        }
    });
}

function exportVoucher() {
    if (voucherId) {
        window.location.href = '{{ url_for("financial.voucher_text_view", id=999999) }}'.replace('999999', voucherId);
    }
}

// 用友风格插行函数
function insertRow() {
    insertUFRow();
}

function insertUFRow() {
    const $focusedInput = $('.uf-form-control:focus');
    if ($focusedInput.length) {
        const $currentRow = $focusedInput.closest('tr');
        insertRowAbove($currentRow[0]);
    } else {
        addUFRow();
    }
}

function insertRowAbove(element) {
    if (voucherMode === 'view') {
        return;
    }

    const $row = $(element).closest('tr');
    const rowIndex = $row.index();

    const operationsColumn = voucherMode !== 'view' ? `
        <td class="uf-operations-cell">
            <button class="uf-btn uf-btn-sm" onclick="insertRowAbove(this)" title="插行">
                <i class="uf-icon">⬆️</i>
            </button>
            <button class="uf-btn uf-btn-sm" onclick="deleteUFRow(this)" title="删行">
                <i class="uf-icon">🗑️</i>
            </button>
        </td>
    ` : '';

    const newRow = `
        <tr class="uf-voucher-row" data-row="${rowIndex + 1}">
            <td class="uf-line-number">${rowIndex + 1}</td>
            <td class="uf-summary-cell">
                <textarea class="uf-form-control uf-summary-input" placeholder="摘要" rows="2"></textarea>
            </td>
            <td class="uf-subject-cell">
                <div class="uf-subject-selector">
                    <div class="uf-subject-input-container">
                        <input type="text" class="uf-form-control uf-subject-input"
                               placeholder="输入科目编码/名称/拼音首字母"
                               autocomplete="off">
                        <div class="uf-subject-dropdown" style="display: none;">
                            <div class="uf-subject-dropdown-list">
                                <!-- 科目选项将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                    <input type="hidden" class="uf-subject-id">
                    <input type="hidden" class="uf-subject-code">
                    <input type="hidden" class="uf-subject-name">
                </div>
            </td>
            <td class="uf-amount-cell">
                <div class="uf-amount-input-container">
                    <input type="text" class="uf-form-control uf-amount-input uf-debit-amount"
                           placeholder="" data-amount-type="debit">
                </div>
            </td>
            <td class="uf-amount-cell">
                <div class="uf-amount-input-container">
                    <input type="text" class="uf-form-control uf-amount-input uf-credit-amount"
                           placeholder="" data-amount-type="credit">
                </div>
            </td>
            ${operationsColumn}
        </tr>
    `;

    $row.before(newRow);
    updateUFLineNumbers();

    // 聚焦到新行的摘要输入框
    const $newRow = $row.prev();
    const $summaryInput = $newRow.find('.uf-summary-input');
    setTimeout(() => {
        $summaryInput.focus();
    }, 100);
}

// 用友风格复制行函数
function copyRow() {
    const $focusedInput = $('.uf-form-control:focus');
    if ($focusedInput.length) {
        const $currentRow = $focusedInput.closest('tr');
        const $newRow = $currentRow.clone();

        // 清空新行的序号和ID
        $newRow.find('.uf-line-number').text('');
        $newRow.removeAttr('data-row');
        $newRow.removeAttr('data-detail-id');

        $currentRow.after($newRow);
        updateUFLineNumbers();
    }
}

// 时间更新函数
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    $('#current-time').text(timeString);
}

// ===== 智能科目选择器功能 =====

// 显示科目下拉框
function showSubjectDropdown(input) {
    const $input = $(input);
    const $container = $input.closest('.uf-subject-selector');
    const $dropdown = $container.find('.uf-subject-dropdown');

    // 隐藏其他下拉框
    $('.uf-subject-dropdown').not($dropdown).hide();

    // 构建科目选项
    buildSubjectOptions($dropdown);

    // 显示下拉框
    $dropdown.show();

    console.log('📋 显示科目下拉框');
}

// 构建科目选项
function buildSubjectOptions($dropdown) {
    const $list = $dropdown.find('.uf-subject-dropdown-list');
    $list.empty();

    if (!subjects || subjects.length === 0) {
        $list.html('<div class="uf-subject-no-results">暂无科目数据</div>');
        return;
    }

    // 添加搜索提示
    $list.append('<div class="uf-subject-search-hint">💡 支持科目编码、名称、拼音首字母搜索</div>');

    // 显示所有科目（最多20个）
    const displaySubjects = subjects.slice(0, 20);
    displaySubjects.forEach(subject => {
        const optionHtml = `
            <div class="uf-subject-option"
                 data-subject-id="${subject.id}"
                 data-subject-code="${subject.code}"
                 data-subject-name="${subject.name}"
                 data-pinyin="${subject.pinyin || ''}">
                <span class="uf-subject-option-code">${subject.code}</span>
                <span class="uf-subject-option-name">${subject.name}</span>
            </div>
        `;
        $list.append(optionHtml);
    });

    if (subjects.length > 20) {
        $list.append('<div class="uf-subject-search-hint">显示前20个科目，请输入关键词筛选</div>');
    }
}

// 筛选科目
function filterSubjects(input) {
    const $input = $(input);
    const searchText = $input.val().trim().toLowerCase();
    const $container = $input.closest('.uf-subject-selector');
    const $dropdown = $container.find('.uf-subject-dropdown');
    const $list = $dropdown.find('.uf-subject-dropdown-list');

    if (!searchText) {
        buildSubjectOptions($dropdown);
        return;
    }

    // 筛选科目
    const filteredSubjects = subjects.filter(subject => {
        const codeMatch = subject.code.toLowerCase().includes(searchText);
        const nameMatch = subject.name.toLowerCase().includes(searchText);
        const pinyinMatch = subject.pinyin && subject.pinyin.toLowerCase().includes(searchText);
        return codeMatch || nameMatch || pinyinMatch;
    });

    $list.empty();

    if (filteredSubjects.length === 0) {
        $list.html('<div class="uf-subject-no-results">未找到匹配的科目</div>');
        return;
    }

    // 显示筛选结果（最多15个）
    const displaySubjects = filteredSubjects.slice(0, 15);
    displaySubjects.forEach((subject, index) => {
        const optionHtml = `
            <div class="uf-subject-option ${index === 0 ? 'selected' : ''}"
                 data-subject-id="${subject.id}"
                 data-subject-code="${subject.code}"
                 data-subject-name="${subject.name}"
                 data-pinyin="${subject.pinyin || ''}">
                <span class="uf-subject-option-code">${subject.code}</span>
                <span class="uf-subject-option-name">${subject.name}</span>
            </div>
        `;
        $list.append(optionHtml);
    });

    if (filteredSubjects.length > 15) {
        $list.append('<div class="uf-subject-search-hint">显示前15个匹配结果</div>');
    }

    console.log(`🔍 筛选科目: "${searchText}" -> ${filteredSubjects.length} 个结果`);
}

// 处理键盘事件
function handleSubjectKeydown(e, input) {
    const $input = $(input);
    const $container = $input.closest('.uf-subject-selector');
    const $dropdown = $container.find('.uf-subject-dropdown');
    const $options = $dropdown.find('.uf-subject-option');

    switch(e.key) {
        case 'ArrowDown':
            e.preventDefault();
            navigateOptions($options, 'down');
            break;
        case 'ArrowUp':
            e.preventDefault();
            navigateOptions($options, 'up');
            break;
        case 'Enter':
            e.preventDefault();
            const $selected = $options.filter('.selected');
            if ($selected.length) {
                selectSubject($selected[0]);
            }
            break;
        case 'Escape':
            e.preventDefault();
            $dropdown.hide();
            break;
        case 'Tab':
            // Tab键自动选择第一个匹配项
            const $first = $options.first();
            if ($first.length) {
                selectSubject($first[0]);
            }
            break;
    }
}

// 导航选项
function navigateOptions($options, direction) {
    const $current = $options.filter('.selected');
    let $next;

    if ($current.length === 0) {
        $next = $options.first();
    } else {
        $current.removeClass('selected');
        if (direction === 'down') {
            $next = $current.next('.uf-subject-option');
            if ($next.length === 0) {
                $next = $options.first();
            }
        } else {
            $next = $current.prev('.uf-subject-option');
            if ($next.length === 0) {
                $next = $options.last();
            }
        }
    }

    $next.addClass('selected');

    // 滚动到可见区域
    const dropdown = $next.closest('.uf-subject-dropdown')[0];
    const option = $next[0];
    if (dropdown && option) {
        const dropdownRect = dropdown.getBoundingClientRect();
        const optionRect = option.getBoundingClientRect();

        if (optionRect.bottom > dropdownRect.bottom) {
            dropdown.scrollTop += optionRect.bottom - dropdownRect.bottom;
        } else if (optionRect.top < dropdownRect.top) {
            dropdown.scrollTop -= dropdownRect.top - optionRect.top;
        }
    }
}

// 选择科目
function selectSubject(option) {
    const $option = $(option);
    const $container = $option.closest('.uf-subject-selector');
    const $input = $container.find('.uf-subject-input');
    const $dropdown = $container.find('.uf-subject-dropdown');

    const subjectId = $option.data('subject-id');
    const subjectCode = $option.data('subject-code');
    const subjectName = $option.data('subject-name');

    // 填充数据
    $input.val(`${subjectCode} ${subjectName}`);
    $input.addClass('has-value');
    $container.find('.uf-subject-id').val(subjectId);
    $container.find('.uf-subject-code').val(subjectCode);
    $container.find('.uf-subject-name').val(subjectName);

    // 隐藏下拉框
    $dropdown.hide();

    // 移动到下一个输入框（借方金额）
    const $row = $container.closest('tr');
    const $debitInput = $row.find('.uf-debit-amount');
    setTimeout(() => {
        $debitInput.focus().select();
    }, 100);

    console.log(`✅ 选择科目: ${subjectCode} ${subjectName}`);
}

// 获取科目拼音首字母（简化版）
function getSubjectPinyin(name) {
    // 这里可以集成更完整的拼音库，现在使用简化版
    const pinyinMap = {
        '现金': 'xj', '银行存款': 'yhck', '应收账款': 'yszk', '库存商品': 'kcsp',
        '固定资产': 'gdzc', '应付账款': 'yfzk', '短期借款': 'dqjk', '实收资本': 'sszb',
        '主营业务收入': 'zyywsr', '主营业务成本': 'zyywcb', '管理费用': 'glfy', '财务费用': 'cwfy'
    };
    return pinyinMap[name] || name.charAt(0).toLowerCase();
}

// ===== 用友风格金额格式化函数 =====

// 准备金额编辑（聚焦时）
function prepareUFAmountEdit(input) {
    const $input = $(input);
    const value = $input.val();

    if (value) {
        // 移除格式化，显示纯数字便于编辑
        const numericValue = parseUFAmount(value);
        if (numericValue > 0) {
            $input.val(numericValue.toString());
        }
    }

    // 隐藏单位
    $input.siblings('.uf-amount-unit').hide();
}

// 实时金额格式化（输入时）
function formatUFAmountInput(input) {
    const $input = $(input);
    let value = $input.val();

    // 只允许数字、小数点和负号
    value = value.replace(/[^\d.-]/g, '');

    // 确保只有一个小数点
    const parts = value.split('.');
    if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
    }

    // 限制小数位数为2位
    if (parts.length === 2 && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
    }

    $input.val(value);

    // 更新样式状态
    updateAmountInputState($input, value);
}

// 完成金额格式化（失焦时）
function finalizeUFAmountFormat(input) {
    const $input = $(input);
    const value = $input.val().trim();

    if (!value || value === '0' || value === '0.00') {
        // 空值或零值
        $input.val('');
        $input.removeClass('has-value zero invalid large-amount');
        $input.siblings('.uf-amount-unit').show();
        return;
    }

    const numericValue = parseFloat(value);

    if (isNaN(numericValue) || numericValue < 0) {
        // 无效值
        $input.addClass('invalid');
        $input.siblings('.uf-amount-unit').show();
        return;
    }

    // 格式化显示
    const formattedValue = formatUFAmountDisplay(numericValue);
    $input.val(formattedValue);

    // 更新样式状态
    updateAmountInputState($input, numericValue);

    // 显示单位
    $input.siblings('.uf-amount-unit').show();

    console.log(`💰 金额格式化完成: ${value} -> ${formattedValue}`);
}

// 格式化金额显示
function formatUFAmountDisplay(amount) {
    if (!amount || amount === 0) return '';

    // 用友风格：千分位分隔符 + 两位小数
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
        useGrouping: true
    });
}

// 解析金额字符串为数字
function parseUFAmount(amountStr) {
    if (!amountStr) return 0;

    // 移除千分位分隔符、货币符号等
    const cleanStr = amountStr.toString()
        .replace(/[¥￥$€£,\s]/g, '')
        .replace(/[，]/g, '');

    const amount = parseFloat(cleanStr);
    return isNaN(amount) ? 0 : amount;
}

// 更新金额输入框状态
function updateAmountInputState($input, value) {
    const numericValue = typeof value === 'number' ? value : parseUFAmount(value);

    // 清除所有状态类
    $input.removeClass('has-value zero invalid large-amount');

    if (numericValue > 0) {
        $input.addClass('has-value');

        // 大金额警告（超过100万）
        if (numericValue >= 1000000) {
            $input.addClass('large-amount');
        }
    } else if (numericValue === 0) {
        $input.addClass('zero');
    } else {
        $input.addClass('invalid');
    }

    // 根据借贷方向设置颜色
    const amountType = $input.data('amount-type');
    if (numericValue > 0) {
        if (amountType === 'debit') {
            $input.addClass('has-value');
        } else if (amountType === 'credit') {
            $input.addClass('has-value');
        }
    }
}

// 验证金额输入
function validateUFAmount(input) {
    const $input = $(input);
    const value = $input.val();
    const numericValue = parseUFAmount(value);

    // 检查是否为有效数字
    if (value && (isNaN(numericValue) || numericValue < 0)) {
        $input.addClass('invalid');
        return false;
    }

    $input.removeClass('invalid');
    return true;
}

// 获取格式化的金额文本（用于显示）
function getFormattedAmountText(amount) {
    if (!amount || amount === 0) return '';

    return formatUFAmountDisplay(amount);
}

// 金额转换为大写（中文）
function convertAmountToChinese(amount) {
    const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    const units = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿'];
    const decimals = ['角', '分'];

    if (amount === 0) return '零元整';

    let result = '';
    const amountStr = amount.toFixed(2);
    const [integerPart, decimalPart] = amountStr.split('.');

    // 处理整数部分
    for (let i = 0; i < integerPart.length; i++) {
        const digit = parseInt(integerPart[i]);
        const unitIndex = integerPart.length - i - 1;

        if (digit !== 0) {
            result += digits[digit] + units[unitIndex];
        } else if (result && !result.endsWith('零')) {
            result += '零';
        }
    }

    result += '元';

    // 处理小数部分
    if (decimalPart && decimalPart !== '00') {
        const jiao = parseInt(decimalPart[0]);
        const fen = parseInt(decimalPart[1]);

        if (jiao > 0) {
            result += digits[jiao] + '角';
        }
        if (fen > 0) {
            result += digits[fen] + '分';
        }
    } else {
        result += '整';
    }

    return result;
}

// ===== 用户体验增强功能 =====

// 显示用友风格消息提示
function showUFMessage(message, type = 'info', duration = 3000) {
    // 移除现有消息
    $('.uf-message').remove();

    // 创建消息元素
    const messageClass = `uf-message uf-message-${type}`;
    const iconMap = {
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'info': 'ℹ️'
    };

    const messageHtml = `
        <div class="${messageClass}" style="
            position: fixed;
            top: 80px;
            right: 20px;
            background: white;
            border: 2px solid ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : type === 'warning' ? '#ff9800' : '#2196F3'};
            border-radius: 4px;
            padding: 12px 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-size: 13px;
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 300px;
            animation: slideInRight 0.3s ease-out;
        ">
            <span style="margin-right: 8px;">${iconMap[type] || 'ℹ️'}</span>
            <span>${message}</span>
        </div>
    `;

    $('body').append(messageHtml);

    // 自动移除消息
    setTimeout(() => {
        $('.uf-message').fadeOut(300, function() {
            $(this).remove();
        });
    }, duration);
}

// 检查是否有未保存的修改
function hasUnsavedChanges() {
    // 简单检查：比较当前表单数据与初始数据
    // 这里可以根据实际需求实现更复杂的检查逻辑
    return false; // 暂时返回false，后续可以完善
}

// 键盘快捷键支持
function initUFKeyboardShortcuts() {
    $(document).on('keydown', function(e) {
        // Ctrl+S: 保存凭证
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            if (voucherMode !== 'view') {
                saveVoucher();
            }
            return false;
        }

        // Ctrl+P: 打印凭证
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printVoucher();
            return false;
        }

        // Ctrl+N: 新建凭证
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            newVoucher();
            return false;
        }

        // F1: 帮助
        if (e.key === 'F1') {
            e.preventDefault();
            showUFHelp();
            return false;
        }

        // F9: 借贷平衡检查
        if (e.key === 'F9') {
            e.preventDefault();
            checkBalance();
            return false;
        }

        // Alt+Left: 上一张凭证
        if (e.altKey && e.key === 'ArrowLeft') {
            e.preventDefault();
            navigateToPrevious();
            return false;
        }

        // Alt+Right: 下一张凭证
        if (e.altKey && e.key === 'ArrowRight') {
            e.preventDefault();
            navigateToNext();
            return false;
        }

        // Escape: 取消当前操作
        if (e.key === 'Escape') {
            e.preventDefault();
            // 关闭模态框或取消编辑
            if ($('.uf-modal:visible').length > 0) {
                closeSubjectModal();
            }
            return false;
        }
    });
}

// 显示帮助信息
function showUFHelp() {
    const helpContent = `
        <div class="uf-help-content" style="text-align: left; line-height: 1.6;">
            <h3 style="margin-bottom: 15px;">🔧 快捷键说明</h3>
            <div class="uf-help-section" style="margin-bottom: 15px;">
                <h4 style="margin-bottom: 8px; color: #333;">基本操作</h4>
                <ul style="margin: 0; padding-left: 20px;">
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">Ctrl+S</kbd> - 保存凭证</li>
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">Ctrl+P</kbd> - 打印凭证</li>
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">Ctrl+N</kbd> - 新建凭证</li>
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">F9</kbd> - 借贷平衡检查</li>
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">Esc</kbd> - 取消当前操作</li>
                </ul>
            </div>
            <div class="uf-help-section" style="margin-bottom: 15px;">
                <h4 style="margin-bottom: 8px; color: #333;">凭证导航</h4>
                <ul style="margin: 0; padding-left: 20px;">
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">Alt+←</kbd> - 上一张凭证</li>
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">Alt+→</kbd> - 下一张凭证</li>
                </ul>
            </div>
            <div class="uf-help-section">
                <h4 style="margin-bottom: 8px; color: #333;">表格操作</h4>
                <ul style="margin: 0; padding-left: 20px;">
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">Tab</kbd> - 移动到下一个单元格</li>
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">Shift+Tab</kbd> - 移动到上一个单元格</li>
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">Enter</kbd> - 移动到下一行</li>
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">F7</kbd> - 选择会计科目</li>
                </ul>
            </div>
        </div>
    `;

    showUFMessage(helpContent, 'info', 8000);
}

// 自动调整摘要文本区高度
function autoResizeTextarea(textarea) {
    // 重置高度以获取正确的scrollHeight
    textarea.style.height = 'auto';

    // 计算所需高度
    const minHeight = 38; // 最小高度
    const maxHeight = 120; // 最大高度
    const scrollHeight = textarea.scrollHeight;

    // 设置新高度
    const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
    textarea.style.height = newHeight + 'px';

    // 如果内容超过最大高度，显示滚动条
    if (scrollHeight > maxHeight) {
        textarea.style.overflowY = 'auto';
    } else {
        textarea.style.overflowY = 'hidden';
    }
}

// 初始化摘要文本区自动调整
function initSummaryAutoResize() {
    // 为所有摘要输入框添加事件监听
    $(document).on('input', '.uf-summary-input', function() {
        autoResizeTextarea(this);
        // 内容变化时可能需要重新调整表格布局
        clearTimeout(window.layoutTimer);
        window.layoutTimer = setTimeout(optimizeTableLayout, 300);
    });

    // 为所有摘要输入框添加键盘事件
    $(document).on('keydown', '.uf-summary-input', function(e) {
        // 延迟执行以确保内容已更新
        setTimeout(() => {
            autoResizeTextarea(this);
        }, 0);
    });

    // 添加粘贴事件处理
    $(document).on('paste', '.uf-summary-input', function() {
        setTimeout(() => {
            autoResizeTextarea(this);
        }, 10);
    });

    // 初始化现有的摘要输入框
    $('.uf-summary-input').each(function() {
        autoResizeTextarea(this);
    });
}

// 优化表格列宽自适应
function optimizeTableLayout() {
    const table = document.querySelector('.uf-voucher-table');
    if (!table) return;

    // 获取表格容器宽度
    const container = table.closest('.uf-voucher-table-container');
    if (!container) return;

    const containerWidth = container.offsetWidth;
    console.log('容器宽度:', containerWidth);

    // 如果容器宽度太小，使用自动布局
    if (containerWidth < 800) {
        table.classList.add('auto-layout');
        console.log('应用自动布局模式');
        return;
    } else {
        table.classList.remove('auto-layout');
    }

    // 计算各列的理想宽度
    const sequenceWidth = 50;
    const operationsWidth = 70;
    const amountWidth = Math.max(120, Math.min(160, containerWidth * 0.15));

    // 计算摘要和科目列的可用宽度
    const fixedWidth = sequenceWidth + operationsWidth + (amountWidth * 2) + 40; // 40px for borders and padding
    const availableWidth = containerWidth - fixedWidth;

    // 智能分配摘要和科目列宽度
    let summaryWidth, subjectWidth;

    if (availableWidth >= 400) {
        // 宽屏：摘要占更多空间
        summaryWidth = Math.max(200, availableWidth * 0.65);
        subjectWidth = Math.max(180, availableWidth * 0.35);
    } else if (availableWidth >= 300) {
        // 中等屏幕：平均分配
        summaryWidth = Math.max(150, availableWidth * 0.55);
        subjectWidth = Math.max(150, availableWidth * 0.45);
    } else {
        // 小屏幕：使用最小宽度
        summaryWidth = 150;
        subjectWidth = 150;
    }

    // 应用样式
    const style = document.createElement('style');
    style.textContent = `
        .uf-voucher-table:not(.auto-layout) .uf-col-sequence {
            width: ${sequenceWidth}px !important;
            min-width: ${sequenceWidth}px !important;
            max-width: ${sequenceWidth}px !important;
        }
        .uf-voucher-table:not(.auto-layout) .uf-col-summary {
            width: ${summaryWidth}px !important;
            min-width: ${Math.min(summaryWidth, 200)}px !important;
        }
        .uf-voucher-table:not(.auto-layout) .uf-col-subject {
            width: ${subjectWidth}px !important;
            min-width: ${Math.min(subjectWidth, 180)}px !important;
        }
        .uf-voucher-table:not(.auto-layout) .uf-col-debit,
        .uf-voucher-table:not(.auto-layout) .uf-col-credit {
            width: ${amountWidth}px !important;
            min-width: ${Math.min(amountWidth, 120)}px !important;
            max-width: ${amountWidth}px !important;
        }
        .uf-voucher-table:not(.auto-layout) .uf-col-operations {
            width: ${operationsWidth}px !important;
            min-width: ${operationsWidth}px !important;
            max-width: ${operationsWidth}px !important;
        }
    `;

    // 移除旧的动态样式
    const oldStyle = document.querySelector('#dynamic-table-style');
    if (oldStyle) {
        oldStyle.remove();
    }

    style.id = 'dynamic-table-style';
    document.head.appendChild(style);

    console.log('表格布局优化完成:', {
        容器宽度: containerWidth,
        序号列: sequenceWidth,
        摘要列: summaryWidth,
        科目列: subjectWidth,
        金额列: amountWidth,
        操作列: operationsWidth
    });
}

// 页面初始化时启用快捷键
$(document).ready(function() {
    initUFKeyboardShortcuts();
    console.log('✅ 用友风格快捷键已启用');

    // 初始化摘要文本区自动调整
    initSummaryAutoResize();
    console.log('✅ 摘要文本区自动调整已启用');

    // 优化表格布局
    optimizeTableLayout();
    console.log('✅ 表格列宽优化已应用');

    // 窗口大小改变时重新优化布局
    let resizeTimer;
    $(window).on('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(() => {
            optimizeTableLayout();
            // 重新调整所有摘要输入框的高度
            $('.uf-summary-input').each(function() {
                autoResizeTextarea(this);
            });
        }, 150);
    });

    // 显示欢迎消息
    if (voucherMode === 'create') {
        showUFMessage('欢迎使用用友风格记账凭证编辑器！按F1查看快捷键帮助', 'info', 5000);
    }

    // 添加页面离开确认
    window.addEventListener('beforeunload', function(e) {
        if (voucherMode !== 'view' && hasUnsavedChanges()) {
            e.preventDefault();
            e.returnValue = '您有未保存的修改，确定要离开吗？';
            return e.returnValue;
        }
    });
});

</script>
{% endblock %}
