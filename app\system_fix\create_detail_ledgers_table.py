"""
创建明细账记录表
"""

from app import db
from sqlalchemy import text
import traceback

def create_detail_ledgers_table():
    """创建明细账记录表"""
    try:
        # 检查表是否已存在
        result = db.session.execute(text("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'detail_ledgers'"))
        if result.scalar() > 0:
            print("detail_ledgers表已存在，无需创建")
            return True
        
        # 创建detail_ledgers表
        create_table_sql = text("""
        CREATE TABLE detail_ledgers (
            id INT IDENTITY(1,1) PRIMARY KEY,
            area_id INT NOT NULL,
            subject_id INT NOT NULL,
            year INT NOT NULL,
            month INT NOT NULL,
            ledger_date DATE NOT NULL,
            opening_balance DECIMAL(12, 2) NOT NULL DEFAULT 0,
            closing_balance DECIMAL(12, 2) NOT NULL DEFAULT 0,
            period_debit DECIMAL(12, 2) NOT NULL DEFAULT 0,
            period_credit DECIMAL(12, 2) NOT NULL DEFAULT 0,
            record_count INT NOT NULL DEFAULT 0,
            status NVARCHAR(20) NOT NULL DEFAULT '已生成',
            generated_by INT NOT NULL,
            generated_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
            notes NTEXT NULL,
            created_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
            updated_at DATETIME2(1) NULL,
            
            -- 外键约束
            CONSTRAINT FK_detail_ledgers_area FOREIGN KEY (area_id) REFERENCES administrative_areas(id),
            CONSTRAINT FK_detail_ledgers_subject FOREIGN KEY (subject_id) REFERENCES accounting_subjects(id),
            CONSTRAINT FK_detail_ledgers_user FOREIGN KEY (generated_by) REFERENCES users(id),
            
            -- 唯一约束 - 每个科目每月只能有一个明细账
            CONSTRAINT UQ_detail_ledgers_area_subject_period UNIQUE (area_id, subject_id, year, month)
        )
        """)
        
        db.session.execute(create_table_sql)
        
        # 创建索引
        create_indexes_sql = [
            text("CREATE INDEX IX_detail_ledgers_area_id ON detail_ledgers(area_id)"),
            text("CREATE INDEX IX_detail_ledgers_subject_id ON detail_ledgers(subject_id)"),
            text("CREATE INDEX IX_detail_ledgers_year_month ON detail_ledgers(year, month)"),
            text("CREATE INDEX IX_detail_ledgers_generated_at ON detail_ledgers(generated_at)")
        ]
        
        for sql in create_indexes_sql:
            db.session.execute(sql)
        
        db.session.commit()
        print("detail_ledgers表创建成功")
        return True
        
    except Exception as e:
        db.session.rollback()
        print(f"创建detail_ledgers表失败: {str(e)}")
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

    from app import create_app
    app = create_app()

    with app.app_context():
        create_detail_ledgers_table()
