import click
from flask.cli import with_appcontext
from app import db
from app.models import User, Role, UserRole

def register_commands(app):
    @app.cli.command('init-db')
    @with_appcontext
    def init_db_command():
        """初始化数据库"""
        db.create_all()
        click.echo('数据库表已创建')

    @app.cli.command('create-admin')
    @with_appcontext
    def create_admin_command():
        """创建管理员账户"""
        from flask import current_app

        # 检查管理员角色是否存在
        admin_role = Role.query.filter_by(name='管理员').first()
        if not admin_role:
            admin_role = Role(
                name='管理员',
                description='系统管理员',
                permissions='{"*": ["*"]}'
            )
            db.session.add(admin_role)
            db.session.commit()

        # 检查管理员用户是否存在
        admin = User.query.filter_by(username=current_app.config['ADMIN_USERNAME']).first()
        if admin is None:
            admin = User(
                username=current_app.config['ADMIN_USERNAME'],
                email=current_app.config['ADMIN_EMAIL'],
                real_name='系统管理员',
                status=1
            )
            admin.set_password(current_app.config['ADMIN_PASSWORD'])
            db.session.add(admin)
            db.session.commit()

            # 添加管理员角色
            user_role = UserRole(user_id=admin.id, role_id=admin_role.id)
            db.session.add(user_role)
            db.session.commit()

            click.echo(f'管理员账户已创建: {current_app.config["ADMIN_USERNAME"]}')
        else:
            click.echo('管理员账户已存在')

    @app.cli.command('init-data')
    @with_appcontext
    def init_data_command():
        """初始化示例数据"""
        # 这里可以添加示例数据的初始化代码
        click.echo('示例数据已初始化')

    @app.cli.command('add-system-settings-tables')
    @with_appcontext
    def add_system_settings_tables_command():
        """添加系统设置相关的数据库表"""
        try:
            from migrations.add_system_settings_tables import run_migration
            run_migration()
            click.echo('系统设置相关的数据库表添加完成')
        except Exception as e:
            click.echo(f'添加系统设置表时出错: {str(e)}')

    @app.cli.command('create-detail-ledgers-table')
    @with_appcontext
    def create_detail_ledgers_table_command():
        """创建明细账记录表"""
        try:
            from app.system_fix.create_detail_ledgers_table import create_detail_ledgers_table
            if create_detail_ledgers_table():
                click.echo('明细账记录表创建完成')
            else:
                click.echo('明细账记录表创建失败')
        except Exception as e:
            click.echo(f'创建明细账记录表时出错: {str(e)}')
