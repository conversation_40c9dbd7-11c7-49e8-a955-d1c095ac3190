{% extends "financial/base.html" %}

{% block page_title %}明细账管理{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务报表</a></span>
<span class="uf-breadcrumb-item active">明细账管理</span>
{% endblock %}

{% block page_actions %}
<div class="uf-toolbar-group">
    <button type="button" class="uf-btn uf-btn-primary" onclick="refreshLedger()">
        <i class="fas fa-sync-alt uf-icon"></i> 刷新
    </button>
    <button type="button" class="uf-btn uf-btn-success" onclick="batchGenerateLedgers()">
        <i class="fas fa-cogs uf-icon"></i> 批量生成
    </button>
    <div class="uf-toolbar-separator"></div>
    <button type="button" class="uf-btn uf-btn-info" onclick="exportDetailLedger()" {% if not selected_subject %}disabled{% endif %}>
        <i class="fas fa-file-excel uf-icon"></i> 导出
    </button>
    <button type="button" class="uf-btn uf-btn-secondary" onclick="printDetailLedger()" {% if not selected_subject %}disabled{% endif %}>
        <i class="fas fa-print uf-icon"></i> 打印
    </button>
</div>
{% endblock %}

{% block financial_content %}
<!-- 用友风格查询条件 - 紧凑布局 -->
<div class="uf-card uf-query-card">
    <div class="uf-card-body" style="padding: 12px 16px;">
        <form method="GET" class="uf-query-form">
            <div class="uf-query-row">
                <!-- 查询条件组 -->
                <div class="uf-query-conditions">
                    <div class="uf-form-group">
                        <label class="uf-form-label">会计科目：</label>
                        <select class="uf-form-control uf-subject-select" id="subject_id" name="subject_id" required>
                            <option value="">请选择科目</option>
                            {% for subject in subjects %}
                            <option value="{{ subject.id }}" {% if subject.id == subject_id %}selected{% endif %}>
                                {{ subject.code }} - {{ subject.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="uf-form-group">
                        <label class="uf-form-label">年份：</label>
                        <select class="uf-form-control" id="year" name="year" required>
                            {% for y in range(2020, 2030) %}
                            <option value="{{ y }}" {% if y == year %}selected{% endif %}>{{ y }}年</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="uf-form-group">
                        <label class="uf-form-label">月份：</label>
                        <select class="uf-form-control" id="month" name="month" required>
                            {% for m in range(1, 13) %}
                            <option value="{{ m }}" {% if m == month %}selected{% endif %}>{{ m }}月</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <!-- 操作按钮组 -->
                <div class="uf-query-actions">
                    <button type="submit" class="uf-btn uf-btn-primary">
                        <i class="fas fa-search uf-icon"></i> 查询
                    </button>
                    <button type="button" class="uf-btn uf-btn-success" onclick="generateLedger()">
                        <i class="fas fa-cogs uf-icon"></i> 生成
                    </button>
                    <button type="button" class="uf-btn uf-btn-warning" onclick="regenerateLedger()" {% if not selected_subject %}disabled{% endif %}>
                        <i class="fas fa-redo uf-icon"></i> 重生成
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

{% if generation_status %}
<!-- 用友风格生成状态提示 -->
<div style="margin-bottom: 10px;">
    {% if generation_status.success %}
    <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 2px; padding: 12px; color: #155724;">
        <i class="fas fa-check-circle uf-icon"></i> {{ generation_status.message }}
        <br><small style="font-size: 11px;">记录数：{{ generation_status.records_count }}，期初余额：¥{{ "%.2f"|format(generation_status.opening_balance) }}，期末余额：¥{{ "%.2f"|format(generation_status.closing_balance) }}</small>
    </div>
    {% else %}
    <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 2px; padding: 12px; color: #721c24;">
        <i class="fas fa-exclamation-circle uf-icon"></i> {{ generation_status.message }}
    </div>
    {% endif %}
</div>
{% endif %}

{% if selected_subject and ledger_data %}
<!-- 用友风格科目信息 -->
<div class="uf-card" style="margin-bottom: 10px;">
    <div class="uf-card-header">
        <i class="fas fa-list-alt uf-icon"></i> {{ ledger_data.subject.code }} - {{ ledger_data.subject.name }}
    </div>
    <div class="uf-card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; font-size: 12px;">
            <div>
                <strong>科目类型：</strong>{{ ledger_data.subject.subject_type }}
            </div>
            <div>
                <strong>余额方向：</strong>{{ ledger_data.subject.balance_direction }}
            </div>
            <div>
                <strong>期初余额：</strong><span class="uf-amount">¥{{ "%.2f"|format(ledger_data.opening_balance) }}</span>
            </div>
            <div>
                <strong>期末余额：</strong><span class="uf-amount">¥{{ "%.2f"|format(ledger_data.closing_balance) }}</span>
            </div>
            <div>
                <strong>本期借方：</strong><span class="uf-amount">¥{{ "%.2f"|format(ledger_data.total_debit) }}</span>
            </div>
            <div>
                <strong>本期贷方：</strong><span class="uf-amount">¥{{ "%.2f"|format(ledger_data.total_credit) }}</span>
            </div>
            <div>
                <strong>发生笔数：</strong>{{ ledger_data.transaction_count }}
            </div>
            <div>
                <strong>账页期间：</strong>{{ year }}年{{ month }}月
            </div>
        </div>
    </div>
</div>

<!-- 用友财务软件专业明细账表格 -->
<div class="uf-card uf-ledger-card">
    <div class="uf-card-header">
        <div class="uf-card-header-title">
            <i class="fas fa-list-alt uf-card-header-icon"></i>
            明细账记录
        </div>
        <div class="uf-card-header-actions">
            {% if ledger_data %}
            <span class="uf-record-count">共 {{ ledger_data.records|length }} 条记录</span>
            <div class="uf-header-tools">
                <button type="button" class="uf-btn uf-btn-sm uf-btn-outline" onclick="toggleTableView()">
                    <i class="fas fa-expand-arrows-alt uf-icon"></i>
                </button>
                <button type="button" class="uf-btn uf-btn-sm uf-btn-outline" onclick="refreshTable()">
                    <i class="fas fa-sync-alt uf-icon"></i>
                </button>
            </div>
            {% endif %}
        </div>
    </div>
    <div class="uf-card-body" style="padding: 0;">
        <div class="uf-table-container">
            <table class="uf-table uf-ledger-table" id="detailLedgerTable">
                <thead>
                    <tr>
                        <th class="uf-col-line-no">行号</th>
                        <th class="uf-col-date sortable" onclick="sortTable(1)">
                            日期 <i class="fas fa-sort uf-sort-icon"></i>
                        </th>
                        <th class="uf-col-voucher sortable" onclick="sortTable(2)">
                            凭证号 <i class="fas fa-sort uf-sort-icon"></i>
                        </th>
                        <th class="uf-col-summary">摘要</th>
                        <th class="uf-col-debit sortable" onclick="sortTable(4)">
                            借方金额 <i class="fas fa-sort uf-sort-icon"></i>
                        </th>
                        <th class="uf-col-credit sortable" onclick="sortTable(5)">
                            贷方金额 <i class="fas fa-sort uf-sort-icon"></i>
                        </th>
                        <th class="uf-col-balance sortable" onclick="sortTable(6)">
                            余额 <i class="fas fa-sort uf-sort-icon"></i>
                        </th>
                        <th class="uf-col-operations">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in ledger_data.records %}
                    <tr class="uf-ledger-row {% if record.get('is_opening') %}uf-opening-row{% elif record.get('is_closing') %}uf-closing-row{% endif %}"
                        data-line="{{ record.line_number }}" data-voucher="{{ record.voucher_number or '' }}">
                        <td class="uf-line-number">{{ record.line_number }}</td>
                        <td class="uf-date-cell">
                            {% if record.voucher_date %}
                                <span class="uf-date">{{ record.voucher_date.strftime('%Y-%m-%d') if record.voucher_date.strftime else record.voucher_date }}</span>
                            {% else %}
                                <span class="uf-empty">-</span>
                            {% endif %}
                        </td>
                        <td class="uf-voucher-cell">
                            {% if record.voucher_number %}
                                <span class="uf-voucher-number">{{ record.voucher_number }}</span>
                            {% else %}
                                <span class="uf-empty">-</span>
                            {% endif %}
                        </td>
                        <td class="uf-summary-cell">
                            <div class="uf-summary-content">
                                {% if record.get('is_opening') %}
                                    <strong class="uf-opening-text">{{ record.summary }}</strong>
                                {% elif record.get('is_closing') %}
                                    <strong class="uf-closing-text">{{ record.summary }}</strong>
                                {% else %}
                                    <span class="uf-normal-text">{{ record.summary }}</span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="uf-amount-cell uf-debit-cell">
                            {% if record.debit_amount > 0 %}
                                <span class="uf-amount">{{ "{:,.2f}"|format(record.debit_amount) }}</span>
                            {% else %}
                                <span class="uf-amount-zero">-</span>
                            {% endif %}
                        </td>
                        <td class="uf-amount-cell uf-credit-cell">
                            {% if record.credit_amount > 0 %}
                                <span class="uf-amount">{{ "{:,.2f}"|format(record.credit_amount) }}</span>
                            {% else %}
                                <span class="uf-amount-zero">-</span>
                            {% endif %}
                        </td>
                        <td class="uf-amount-cell uf-balance-cell">
                            <span class="uf-balance {% if record.balance > 0 %}uf-positive{% elif record.balance < 0 %}uf-negative{% else %}uf-zero{% endif %}">
                                {{ "{:,.2f}"|format(record.balance) }}
                            </span>
                        </td>
                        <td class="uf-operations-cell">
                            {% if record.get('voucher_id') %}
                                <div class="uf-btn-group-sm">
                                    <a href="{{ url_for('financial.view_voucher', id=record.voucher_id) }}"
                                       class="uf-btn uf-btn-xs uf-btn-info" title="查看凭证">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="uf-btn uf-btn-xs uf-btn-secondary"
                                            onclick="showVoucherPreview({{ record.voucher_id }})" title="预览">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            {% else %}
                                <span class="uf-empty">-</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
        </table>
    </div>
</div>

{% else %}
<div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 2px; padding: 16px; text-align: center; color: #856404;">
    <i class="fas fa-info-circle uf-icon"></i> 请选择会计科目和年月，然后点击"查看明细账"或"生成明细账"
</div>
{% endif %}

{% if not selected_subject %}
<!-- 用友风格使用说明 -->
<div class="uf-card" style="margin-top: 10px;">
    <div class="uf-card-header">
        <i class="fas fa-info-circle uf-icon"></i> 明细账功能说明
    </div>
    <div class="uf-card-body">
        <div style="font-size: 12px; line-height: 1.6;">
            <h6 style="color: var(--uf-primary); margin-bottom: 8px; font-size: 13px;">什么是明细账？</h6>
            <p style="margin-bottom: 12px;">明细账是按照会计科目设置的，用来分类登记某一类经济业务，提供有关明细核算资料的账簿。</p>

            <h6 style="color: var(--uf-primary); margin-bottom: 8px; font-size: 13px;">明细账的特点：</h6>
            <ul style="margin-bottom: 12px; padding-left: 20px;">
                <li style="margin-bottom: 4px;"><strong>按月生成</strong>：每个科目按月份生成独立的明细账页</li>
                <li style="margin-bottom: 4px;"><strong>连续记录</strong>：从期初余额开始，按时间顺序记录每笔业务</li>
                <li style="margin-bottom: 4px;"><strong>余额结转</strong>：每笔业务后自动计算并更新余额</li>
                <li style="margin-bottom: 4px;"><strong>标准格式</strong>：符合会计账簿的标准格式要求</li>
            </ul>

            <h6 style="color: var(--uf-primary); margin-bottom: 8px; font-size: 13px;">使用步骤：</h6>
            <ol style="margin: 0; padding-left: 20px;">
                <li style="margin-bottom: 4px;">选择要查看的会计科目</li>
                <li style="margin-bottom: 4px;">选择年份和月份</li>
                <li style="margin-bottom: 4px;">点击"生成明细账"按钮（首次使用）</li>
                <li style="margin-bottom: 4px;">点击"查看明细账"查看已生成的明细账</li>
                <li style="margin-bottom: 0;">可以批量生成所有有发生额科目的明细账</li>
            </ol>
        </div>
    </div>
</div>
{% endif %}


{% endblock %}

{% block financial_js %}
<script>
// 用友风格明细账页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initDetailLedgerPage();
});

function initDetailLedgerPage() {
    // 初始化表格功能
    initTableFeatures();

    // 初始化快捷键
    initKeyboardShortcuts();

    // 初始化工具提示
    initTooltips();

    // 初始化科目选择器
    initSubjectSelector();
}

function initTableFeatures() {
    const table = document.getElementById('detailLedgerTable');
    if (table) {
        // 添加表格行悬停效果
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.classList.add('uf-row-hover');
            });
            row.addEventListener('mouseleave', function() {
                this.classList.remove('uf-row-hover');
            });
        });
    }
}

function initKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // F5: 刷新
        if (e.key === 'F5') {
            e.preventDefault();
            refreshLedger();
        }

        // Ctrl+E: 导出
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            exportDetailLedger();
        }

        // Ctrl+P: 打印
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printDetailLedger();
        }

        // Ctrl+G: 生成
        if (e.ctrlKey && e.key === 'g') {
            e.preventDefault();
            generateLedger();
        }
    });
}

function initTooltips() {
    // 为金额单元格添加工具提示
    const amountCells = document.querySelectorAll('.uf-amount');
    amountCells.forEach(cell => {
        const value = cell.textContent.replace(/[,]/g, '');
        if (value && value !== '-') {
            cell.title = `金额：${value}`;
        }
    });
}

function initSubjectSelector() {
    const subjectSelect = document.getElementById('subject_id');
    if (subjectSelect) {
        // 添加搜索功能
        subjectSelect.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                document.querySelector('.uf-query-form').submit();
            }
        });
    }
}

// 表格排序功能
let sortDirection = {};

function sortTable(columnIndex) {
    const table = document.getElementById('detailLedgerTable');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr:not(.uf-opening-row):not(.uf-closing-row)'));

    if (rows.length === 0) return;

    // 获取当前排序方向
    const currentDirection = sortDirection[columnIndex] || 'asc';
    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
    sortDirection[columnIndex] = newDirection;

    // 更新排序图标
    updateSortIcons(columnIndex, newDirection);

    // 排序行
    rows.sort((a, b) => {
        const aValue = a.cells[columnIndex].textContent.trim();
        const bValue = b.cells[columnIndex].textContent.trim();

        // 数字列排序
        if (columnIndex >= 4 && columnIndex <= 6) {
            const aNum = parseFloat(aValue.replace(/[,\-]/g, '')) || 0;
            const bNum = parseFloat(bValue.replace(/[,\-]/g, '')) || 0;
            return newDirection === 'asc' ? aNum - bNum : bNum - aNum;
        }

        // 日期列排序
        if (columnIndex === 1) {
            const aDate = new Date(aValue);
            const bDate = new Date(bValue);
            return newDirection === 'asc' ? aDate - bDate : bDate - aDate;
        }

        // 文本列排序
        return newDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    });

    // 重新插入排序后的行
    rows.forEach(row => tbody.appendChild(row));
}

function updateSortIcons(activeColumn, direction) {
    // 清除所有排序图标
    document.querySelectorAll('.uf-sort-icon').forEach(icon => {
        icon.className = 'fas fa-sort uf-sort-icon';
    });

    // 设置当前列的排序图标
    const activeIcon = document.querySelector(`th:nth-child(${activeColumn + 1}) .uf-sort-icon`);
    if (activeIcon) {
        activeIcon.className = `fas fa-sort-${direction === 'asc' ? 'up' : 'down'} uf-sort-icon uf-sort-active`;
    }
}

// 刷新明细账
function refreshLedger() {
    window.location.reload();
}

// 切换表格视图
function toggleTableView() {
    const table = document.getElementById('detailLedgerTable');
    const container = table.closest('.uf-table-container');

    if (container.classList.contains('uf-fullscreen')) {
        container.classList.remove('uf-fullscreen');
    } else {
        container.classList.add('uf-fullscreen');
    }
}

// 刷新表格
function refreshTable() {
    const subjectId = document.getElementById('subject_id').value;
    const year = document.getElementById('year').value;
    const month = document.getElementById('month').value;

    if (subjectId && year && month) {
        window.location.href = `?subject_id=${subjectId}&year=${year}&month=${month}`;
    }
}

// 显示凭证预览
function showVoucherPreview(voucherId) {
    // 创建预览窗口
    const previewUrl = `/financial/vouchers/${voucherId}/preview`;
    const previewWindow = window.open(previewUrl, 'voucherPreview',
        'width=800,height=600,scrollbars=yes,resizable=yes');

    if (previewWindow) {
        previewWindow.focus();
    }
}

// 生成单个科目明细账
function generateLedger() {
    const subjectId = document.getElementById('subject_id').value;
    const year = document.getElementById('year').value;
    const month = document.getElementById('month').value;

    if (!subjectId) {
        alert('请先选择科目');
        return;
    }

    if (!year || !month) {
        alert('请选择年份和月份');
        return;
    }

    // 显示加载状态
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
    btn.disabled = true;

    fetch('{{ url_for("financial.generate_detail_ledger_api") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            subject_id: parseInt(subjectId),
            year: parseInt(year),
            month: parseInt(month)
        })
    })
    .then(response => {
        // 检查响应状态
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // 检查响应内容类型
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('服务器返回的不是JSON格式的数据');
        }

        return response.json();
    })
    .then(data => {
        if (data.success) {
            alert(data.message);
            // 重新加载页面显示生成的明细账
            window.location.href = `?subject_id=${subjectId}&year=${year}&month=${month}`;
        } else {
            alert('生成失败：' + data.message);
        }
    })
    .catch(error => {
        console.error('生成明细账错误:', error);
        alert('生成失败：' + error.message);
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// 批量生成明细账
function batchGenerateLedgers() {
    const year = document.getElementById('year').value;
    const month = document.getElementById('month').value;

    if (!year || !month) {
        alert('请选择年份和月份');
        return;
    }

    if (!confirm(`确定要批量生成 ${year}年${month}月 所有有发生额科目的明细账吗？`)) {
        return;
    }

    // 显示加载状态
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 批量生成中...';
    btn.disabled = true;

    fetch('{{ url_for("financial.batch_generate_detail_ledgers") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            year: parseInt(year),
            month: parseInt(month),
            subject_ids: []  // 空数组表示所有有发生额的科目
        })
    })
    .then(response => {
        // 检查响应状态
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // 检查响应内容类型
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('服务器返回的不是JSON格式的数据');
        }

        return response.json();
    })
    .then(data => {
        if (data.success) {
            alert(data.message);
            // 刷新页面
            window.location.reload();
        } else {
            alert('批量生成失败：' + data.message);
        }
    })
    .catch(error => {
        console.error('批量生成明细账错误:', error);
        alert('批量生成失败：' + error.message);
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// 重新生成明细账
function regenerateLedger() {
    if (!confirm('确定要重新生成明细账吗？这将覆盖现有的明细账数据。')) {
        return;
    }
    generateLedger();
}

// 导出明细账
function exportDetailLedger() {
    const subjectId = document.getElementById('subject_id').value;
    const year = document.getElementById('year').value;
    const month = document.getElementById('month').value;

    if (!subjectId || !year || !month) {
        alert('请先选择科目和年月');
        return;
    }

    const startDate = `${year}-${month.padStart(2, '0')}-01`;
    const url = `{{ url_for('financial.export_report', report_type='detail_ledger') }}?subject_id=${subjectId}&start_date=${startDate}&end_date=${startDate}`;
    window.open(url, '_blank');
}

// 打印明细账
function printDetailLedger() {
    window.print();
}

// 科目选择变化时的处理
document.getElementById('subject_id').addEventListener('change', function() {
    // 不自动提交，让用户手动选择操作
});

// 添加明细账表格样式
document.addEventListener('DOMContentLoaded', function() {
    // 为明细账表格添加特殊样式
    const ledgerTable = document.querySelector('.uf-ledger-table');
    if (ledgerTable) {
        ledgerTable.style.fontSize = '11px';
        ledgerTable.style.fontFamily = 'Microsoft YaHei, SimSun, sans-serif';
    }
});
</script>
{% endblock %}

{% block financial_css %}
<style>
/* 用友财务软件专业明细账样式 - 13px字体标准 */

/* 查询卡片样式 */
.uf-query-card {
    margin-bottom: 12px;
    border: 1px solid var(--uf-border);
    box-shadow: var(--uf-box-shadow);
}

.uf-query-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;
}

.uf-query-conditions {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
    min-width: 0;
}

.uf-query-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 0 0 auto;
}

.uf-form-group {
    display: flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
}

.uf-form-label {
    font-size: 13px;
    font-weight: 500;
    color: var(--uf-gray-600);
    white-space: nowrap;
}

.uf-form-control {
    font-size: 13px;
    height: 32px;
    padding: 4px 8px;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    background: var(--uf-white);
}

.uf-subject-select {
    min-width: 200px;
    max-width: 300px;
}

/* 明细账卡片样式 */
.uf-ledger-card {
    border: 1px solid var(--uf-border);
    box-shadow: var(--uf-box-shadow);
}

.uf-card-header-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: var(--uf-primary);
}

.uf-card-header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.uf-record-count {
    font-size: 12px;
    color: var(--uf-muted);
}

.uf-header-tools {
    display: flex;
    gap: 4px;
}

/* 表格容器样式 */
.uf-table-container {
    overflow: auto;
    max-height: 70vh;
    border: 1px solid var(--uf-border);
}

.uf-table-container.uf-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    max-height: 100vh;
    background: var(--uf-white);
}

/* 明细账表格样式 - 13px字体标准 */
.uf-ledger-table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--uf-font-family);
    font-size: 13px;
    background: var(--uf-white);
    table-layout: fixed;
}

.uf-ledger-table th {
    background: linear-gradient(to bottom, var(--uf-primary) 0%, var(--uf-primary-dark) 100%);
    color: white;
    text-align: center;
    vertical-align: middle;
    font-weight: 600;
    font-size: 13px;
    border: 1px solid var(--uf-grid-border);
    padding: 8px 6px;
    height: 36px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.uf-ledger-table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.uf-ledger-table th.sortable:hover {
    background: linear-gradient(to bottom, var(--uf-primary-light) 0%, var(--uf-primary) 100%);
}

.uf-sort-icon {
    margin-left: 4px;
    font-size: 11px;
    opacity: 0.7;
}

.uf-sort-icon.uf-sort-active {
    opacity: 1;
    color: #fff;
}

/* 表格列宽定义 */
.uf-col-line-no { width: 60px; }
.uf-col-date { width: 100px; }
.uf-col-voucher { width: 100px; }
.uf-col-summary { width: auto; min-width: 200px; }
.uf-col-debit { width: 120px; }
.uf-col-credit { width: 120px; }
.uf-col-balance { width: 120px; }
.uf-col-operations { width: 80px; }

/* 表格行样式 */
.uf-ledger-table td {
    vertical-align: middle;
    padding: 6px 8px;
    font-size: 13px;
    border: 1px solid var(--uf-grid-border);
    height: 36px;
    line-height: 1.3;
}

.uf-ledger-row:hover {
    background: var(--uf-row-hover) !important;
}

.uf-ledger-row:nth-child(even) {
    background: var(--uf-gray-50);
}

/* 特殊行样式 */
.uf-opening-row {
    background: linear-gradient(to bottom, #fff3cd 0%, #ffeaa7 100%) !important;
    font-weight: 600;
}

.uf-closing-row {
    background: linear-gradient(to bottom, #d1ecf1 0%, #bee5eb 100%) !important;
    font-weight: 600;
}

.uf-opening-row:hover,
.uf-closing-row:hover {
    opacity: 0.9;
}

/* 单元格样式 */
.uf-line-number {
    text-align: center;
    font-weight: 500;
    background: var(--uf-gray-50);
    color: var(--uf-gray-600);
    font-size: 12px;
}

.uf-date-cell {
    text-align: center;
}

.uf-date {
    font-family: 'Courier New', monospace;
    font-size: 12px;
}

.uf-voucher-cell {
    text-align: center;
}

.uf-voucher-number {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--uf-primary);
    font-size: 12px;
}

.uf-summary-cell {
    text-align: left;
    padding-left: 12px;
}

.uf-summary-content {
    word-wrap: break-word;
    line-height: 1.4;
}

.uf-opening-text,
.uf-closing-text {
    font-weight: 600;
    color: var(--uf-primary);
}

.uf-normal-text {
    color: var(--uf-gray-700);
}

/* 金额单元格样式 */
.uf-amount-cell {
    text-align: right;
    padding-right: 12px;
    font-family: 'Courier New', 'Times New Roman', monospace;
    font-size: 13px;
    white-space: nowrap;
}

.uf-amount {
    font-weight: 500;
    color: var(--uf-gray-700);
}

.uf-amount-zero {
    color: var(--uf-muted);
    font-style: italic;
}

.uf-balance {
    font-weight: 600;
}

.uf-balance.uf-positive {
    color: var(--uf-amount-positive);
}

.uf-balance.uf-negative {
    color: var(--uf-amount-negative);
}

.uf-balance.uf-zero {
    color: var(--uf-amount-zero);
}

.uf-balance-cell {
    background: var(--uf-gray-50);
}

/* 操作单元格样式 */
.uf-operations-cell {
    text-align: center;
}

.uf-btn-group-sm {
    display: flex;
    gap: 2px;
    justify-content: center;
}

.uf-btn-xs {
    padding: 2px 6px;
    font-size: 11px;
    line-height: 1.2;
    border-radius: 2px;
}

.uf-empty {
    color: var(--uf-muted);
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .uf-query-row {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .uf-query-conditions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .uf-query-actions {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .uf-query-conditions {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .uf-form-group {
        width: 100%;
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .uf-form-control {
        width: 100%;
    }

    .uf-subject-select {
        min-width: auto;
        max-width: none;
    }

    .uf-query-actions {
        flex-wrap: wrap;
        gap: 6px;
    }

    .uf-table-container {
        max-height: 60vh;
    }

    .uf-ledger-table {
        font-size: 12px;
    }

    .uf-ledger-table th,
    .uf-ledger-table td {
        padding: 4px 6px;
        font-size: 12px;
    }

    /* 移动端隐藏部分列 */
    .uf-col-line-no {
        display: none;
    }

    .uf-col-summary {
        min-width: 150px;
    }
}

@media (max-width: 480px) {
    .uf-ledger-table {
        font-size: 11px;
    }

    .uf-ledger-table th,
    .uf-ledger-table td {
        padding: 3px 4px;
        font-size: 11px;
    }

    /* 超小屏幕进一步简化 */
    .uf-col-voucher {
        display: none;
    }

    .uf-btn-group-sm {
        flex-direction: column;
        gap: 1px;
    }
}

/* 打印样式 */
@media print {
    .uf-toolbar,
    .uf-query-card,
    .uf-card-header,
    .page-actions,
    .uf-btn,
    .uf-header-tools {
        display: none !important;
    }

    .uf-ledger-card {
        border: none !important;
        box-shadow: none !important;
        margin: 0 !important;
        page-break-inside: avoid;
    }

    .uf-card-body {
        padding: 0 !important;
    }

    .uf-table-container {
        max-height: none !important;
        overflow: visible !important;
        border: none !important;
    }

    .uf-ledger-table {
        font-size: 10px !important;
        page-break-inside: avoid;
    }

    .uf-ledger-table th {
        font-size: 10px !important;
        padding: 3px 4px !important;
        background: #f0f0f0 !important;
        color: #000 !important;
    }

    .uf-ledger-table td {
        font-size: 10px !important;
        padding: 3px 4px !important;
    }

    /* 打印时隐藏操作列 */
    .uf-col-operations {
        display: none !important;
    }

    /* 打印时优化特殊行样式 */
    .uf-opening-row,
    .uf-closing-row {
        background: #f0f0f0 !important;
        font-weight: 600 !important;
    }

    body {
        font-size: 10px !important;
        margin: 0 !important;
    }

    /* 打印页眉 */
    @page {
        margin: 1cm;
        @top-center {
            content: "明细账查询报表";
            font-size: 14px;
            font-weight: bold;
        }
    }
}

/* 工具提示样式 */
.uf-tooltip {
    position: relative;
    cursor: help;
}

.uf-tooltip:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--uf-dark);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
}

/* 加载状态样式 */
.uf-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
}

.uf-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--uf-primary);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: uf-spin 1s linear infinite;
}

@keyframes uf-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
{% endblock %}
