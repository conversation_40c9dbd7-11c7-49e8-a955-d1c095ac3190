<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>总账页面错误修复验证</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #f53f3f 0%, #dc3545 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #f53f3f;
        }
        .header h1 {
            color: #f53f3f;
            margin: 0;
            font-size: 24px;
            font-weight: 700;
        }
        .header .subtitle {
            color: #666;
            font-size: 14px;
            margin-top: 8px;
        }
        .fix-banner {
            background: linear-gradient(135deg, #00B42A, #52C41A);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 16px rgba(0, 180, 42, 0.3);
        }
        .fix-banner h2 {
            margin: 0;
            font-size: 20px;
        }
        .fix-banner p {
            margin: 8px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }
        .error-section {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #f53f3f;
        }
        .error-section h3 {
            color: #f53f3f;
            margin-top: 0;
            font-size: 16px;
        }
        .error-code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            color: #d73a49;
        }
        .fix-section {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #00B42A;
        }
        .fix-section h3 {
            color: #00B42A;
            margin-top: 0;
            font-size: 16px;
        }
        .fix-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        .fix-list li {
            padding: 6px 0;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }
        .fix-list li::before {
            content: "✓";
            color: #00B42A;
            font-weight: bold;
            font-size: 16px;
        }
        .test-button {
            display: block;
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #165DFF, #0D47A1);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-weight: 600;
            margin: 20px 0;
            transition: all 0.3s;
            font-size: 16px;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(22, 93, 255, 0.4);
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .status-error {
            color: #f53f3f;
            font-weight: bold;
        }
        .status-fixed {
            color: #00B42A;
            font-weight: bold;
        }
        .technical-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        .technical-details h3 {
            color: #333;
            margin-top: 0;
        }
        .code-block {
            background: #f1f3f4;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 16px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before-after .before {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 6px;
            padding: 15px;
        }
        .before-after .after {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
            border-radius: 6px;
            padding: 15px;
        }
        .before-after h4 {
            margin-top: 0;
            font-size: 14px;
        }
        .before-after .before h4 {
            color: #f53f3f;
        }
        .before-after .after h4 {
            color: #00B42A;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 总账页面错误修复验证</h1>
            <p class="subtitle">修复模板格式化错误和数据类型问题</p>
        </div>

        <div class="fix-banner">
            <h2>✅ 错误修复完成</h2>
            <p>总账页面的TypeError格式化错误已成功修复</p>
        </div>

        <div class="error-section">
            <h3>🚨 原始错误信息</h3>
            <div class="error-code">
TypeError: not all arguments converted during string formatting
File "app/templates/financial/ledgers/general.html", line 227
{{ "{:,.2f}"|format(item.period_debit|float) }}
            </div>
            <p><strong>错误原因：</strong>模板中使用了不安全的字符串格式化，当数据为None或非数字类型时会导致格式化失败。</p>
        </div>

        <div class="fix-section">
            <h3>🔧 修复措施</h3>
            <ul class="fix-list">
                <li>修复所有金额字段的格式化问题</li>
                <li>添加数据类型检查和默认值处理</li>
                <li>使用安全的字符串格式化方法</li>
                <li>修复总账汇总数据传递问题</li>
                <li>优化试算平衡计算逻辑</li>
            </ul>
        </div>

        <div class="before-after">
            <div class="before">
                <h4>修复前（错误代码）</h4>
                <div class="code-block">
{{ "{:,.2f}"|format(item.period_debit|float) }}
                </div>
                <p>问题：当item.period_debit为None时会抛出TypeError</p>
            </div>
            <div class="after">
                <h4>修复后（安全代码）</h4>
                <div class="code-block">
{{ "{:,.2f}".format(item.period_debit|float|default(0)) }}
                </div>
                <p>解决：使用default过滤器提供默认值，确保格式化安全</p>
            </div>
        </div>

        <div class="technical-details">
            <h3>🔍 技术修复详情</h3>
            
            <h4>1. 模板格式化修复</h4>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>字段</th>
                        <th>修复前</th>
                        <th>修复后</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>期初余额</td>
                        <td class="status-error">{{ "{:,.2f}"|format(item.opening_balance|float) }}</td>
                        <td class="status-fixed">{{ "{:,.2f}".format(item.opening_balance|float|default(0)) }}</td>
                    </tr>
                    <tr>
                        <td>借方发生额</td>
                        <td class="status-error">{{ "{:,.2f}"|format(item.period_debit|float) }}</td>
                        <td class="status-fixed">{{ "{:,.2f}".format(item.period_debit|float|default(0)) }}</td>
                    </tr>
                    <tr>
                        <td>贷方发生额</td>
                        <td class="status-error">{{ "{:,.2f}"|format(item.period_credit|float) }}</td>
                        <td class="status-fixed">{{ "{:,.2f}".format(item.period_credit|float|default(0)) }}</td>
                    </tr>
                    <tr>
                        <td>期末余额</td>
                        <td class="status-error">{{ "{:,.2f}"|format(item.ending_balance|float) }}</td>
                        <td class="status-fixed">{{ "{:,.2f}".format(item.ending_balance|float|default(0)) }}</td>
                    </tr>
                </tbody>
            </table>

            <h4>2. 后端数据修复</h4>
            <div class="code-block">
# 添加汇总数据计算
total_opening = sum(item['opening_balance'] for item in general_ledger_data)
total_debit = sum(item['period_debit'] for item in general_ledger_data)
total_credit = sum(item['period_credit'] for item in general_ledger_data)
total_ending = sum(item['ending_balance'] for item in general_ledger_data)
            </div>

            <h4>3. 试算平衡修复</h4>
            <div class="code-block">
# 安全的差额计算
{% set balance_diff = (total_debit|float|default(0)) - (total_credit|float|default(0)) %}
            </div>
        </div>

        <a href="http://xiaoyuanst.com/financial/ledgers/general" 
           class="test-button" target="_blank">
            🧪 测试修复后的总账页面
        </a>

        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 2px solid #f0f0f0;">
            <h3 style="color: #333; margin-bottom: 15px;">✅ 修复验证要点</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px;">
                <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; border: 1px solid #e9ecef;">
                    <strong>页面加载</strong><br>
                    <small>确认页面正常加载无错误</small>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; border: 1px solid #e9ecef;">
                    <strong>数据显示</strong><br>
                    <small>检查金额格式化正确显示</small>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; border: 1px solid #e9ecef;">
                    <strong>试算平衡</strong><br>
                    <small>验证借贷平衡计算正确</small>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; border: 1px solid #e9ecef;">
                    <strong>汇总统计</strong><br>
                    <small>确认汇总数据计算准确</small>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
