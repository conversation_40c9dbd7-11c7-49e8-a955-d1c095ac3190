<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用友风格财务账簿模块验证</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #165DFF;
        }
        .header h1 {
            color: #165DFF;
            margin: 0;
        }
        .module-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .module-section h2 {
            color: #333;
            margin-top: 0;
            border-bottom: 1px solid #ccc;
            padding-bottom: 10px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .feature-card h3 {
            color: #165DFF;
            margin-top: 0;
            font-size: 16px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .feature-list li {
            padding: 4px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 8px;
        }
        .button-group {
            display: flex;
            gap: 15px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #165DFF;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0D47A1;
        }
        .btn-success {
            background-color: #00B42A;
            color: white;
        }
        .btn-success:hover {
            background-color: #009922;
        }
        .btn-info {
            background-color: #0088CC;
            color: white;
        }
        .btn-info:hover {
            background-color: #0077BB;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .status-improved {
            color: #00B42A;
            font-weight: bold;
        }
        .status-new {
            color: #165DFF;
            font-weight: bold;
        }
        .highlight-box {
            background-color: #e6f2ff;
            border: 1px solid #165DFF;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .highlight-box h3 {
            margin-top: 0;
            color: #165DFF;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>用友风格财务账簿模块验证</h1>
            <p>全面完善明细账和总账模块，统一13px字体标准</p>
        </div>

        <div class="module-section">
            <h2>📊 明细账模块改进</h2>
            <div class="button-group">
                <a href="http://xiaoyuanst.com/financial/ledgers/detail" 
                   class="test-btn btn-primary" target="_blank">
                    📋 测试明细账模块
                </a>
            </div>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>界面优化</h3>
                    <ul class="feature-list">
                        <li>采用用友标准的紧凑布局</li>
                        <li>13px字体统一标准</li>
                        <li>优化查询条件区域</li>
                        <li>增强表格头部工具栏</li>
                        <li>改进按钮图标和样式</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>功能增强</h3>
                    <ul class="feature-list">
                        <li>表格排序功能</li>
                        <li>快捷键支持</li>
                        <li>全屏表格视图</li>
                        <li>凭证预览功能</li>
                        <li>智能工具提示</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>用户体验</h3>
                    <ul class="feature-list">
                        <li>响应式设计优化</li>
                        <li>移动端适配</li>
                        <li>打印样式优化</li>
                        <li>加载状态指示</li>
                        <li>错误处理改进</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="module-section">
            <h2>📈 总账模块改进</h2>
            <div class="button-group">
                <a href="http://xiaoyuanst.com/financial/ledgers/general" 
                   class="test-btn btn-success" target="_blank">
                    📊 测试总账模块
                </a>
            </div>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>界面升级</h3>
                    <ul class="feature-list">
                        <li>用友风格表格设计</li>
                        <li>增强的查询工具栏</li>
                        <li>表格筛选和搜索</li>
                        <li>批量操作支持</li>
                        <li>状态标签美化</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>高级功能</h3>
                    <ul class="feature-list">
                        <li>多列排序支持</li>
                        <li>行选择和批量导出</li>
                        <li>快速查询按钮</li>
                        <li>科目分析功能</li>
                        <li>余额分析报表</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>数据展示</h3>
                    <ul class="feature-list">
                        <li>金额格式化显示</li>
                        <li>科目类型标签</li>
                        <li>余额方向指示</li>
                        <li>空数据友好提示</li>
                        <li>记录数量统计</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="highlight-box">
            <h3>🎯 用友风格设计标准</h3>
            <div class="code-block">
/* 统一字体标准 */
--uf-font-family: 'Microsoft YaHei', 'SimSun', '宋体', Arial, sans-serif;
--uf-font-size: 13px;           /* 基本字体大小 */
--uf-font-size-large: 14px;     /* 标题字体 */
--uf-font-size-small: 12px;     /* 辅助信息 */
--uf-line-height: 1.3;          /* 行高标准 */

/* 用友色彩体系 */
--uf-primary: #165DFF;          /* 主色调 */
--uf-success: #00B42A;          /* 成功色 */
--uf-warning: #FF7D00;          /* 警告色 */
--uf-danger: #F53F3F;           /* 危险色 */
--uf-info: #0088CC;             /* 信息色 */
            </div>
        </div>

        <div class="module-section">
            <h2>📋 改进对比表</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>功能项目</th>
                        <th>改进前</th>
                        <th>改进后</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>字体大小</td>
                        <td>11px 不统一</td>
                        <td>13px 统一标准</td>
                        <td class="status-improved">✓ 已改进</td>
                    </tr>
                    <tr>
                        <td>表格排序</td>
                        <td>不支持</td>
                        <td>多列排序支持</td>
                        <td class="status-new">✓ 新增</td>
                    </tr>
                    <tr>
                        <td>表格筛选</td>
                        <td>基础筛选</td>
                        <td>实时搜索筛选</td>
                        <td class="status-improved">✓ 已改进</td>
                    </tr>
                    <tr>
                        <td>批量操作</td>
                        <td>不支持</td>
                        <td>行选择和批量导出</td>
                        <td class="status-new">✓ 新增</td>
                    </tr>
                    <tr>
                        <td>响应式设计</td>
                        <td>基础适配</td>
                        <td>完整移动端优化</td>
                        <td class="status-improved">✓ 已改进</td>
                    </tr>
                    <tr>
                        <td>快捷键支持</td>
                        <td>不支持</td>
                        <td>F5刷新、Ctrl+E导出等</td>
                        <td class="status-new">✓ 新增</td>
                    </tr>
                    <tr>
                        <td>打印样式</td>
                        <td>基础样式</td>
                        <td>专业打印优化</td>
                        <td class="status-improved">✓ 已改进</td>
                    </tr>
                    <tr>
                        <td>用户体验</td>
                        <td>基础功能</td>
                        <td>工具提示、加载状态等</td>
                        <td class="status-improved">✓ 已改进</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="module-section">
            <h2>🧪 测试步骤</h2>
            <ol style="line-height: 1.6;">
                <li><strong>明细账测试</strong>：
                    <ul>
                        <li>选择会计科目和查询期间</li>
                        <li>测试表格排序功能</li>
                        <li>验证响应式布局</li>
                        <li>测试快捷键操作</li>
                    </ul>
                </li>
                <li><strong>总账测试</strong>：
                    <ul>
                        <li>测试查询条件和快速查询</li>
                        <li>验证表格筛选和搜索</li>
                        <li>测试批量选择和导出</li>
                        <li>检查试算平衡功能</li>
                    </ul>
                </li>
                <li><strong>样式验证</strong>：
                    <ul>
                        <li>检查13px字体是否统一</li>
                        <li>验证用友色彩体系</li>
                        <li>测试打印样式效果</li>
                        <li>检查移动端适配</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="highlight-box">
            <h3>🎉 预期效果</h3>
            <ul style="line-height: 1.6;">
                <li>✅ 界面风格完全符合用友财务软件标准</li>
                <li>✅ 13px字体统一，界面清晰易读</li>
                <li>✅ 表格功能丰富，操作便捷高效</li>
                <li>✅ 响应式设计，各设备完美适配</li>
                <li>✅ 用户体验显著提升</li>
                <li>✅ 符合财务软件专业标准</li>
            </ul>
        </div>
    </div>
</body>
</html>
