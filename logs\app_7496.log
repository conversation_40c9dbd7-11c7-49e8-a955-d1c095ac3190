2025-06-21 17:26:05,812 INFO: 应用启动 - PID: 7496 [in C:\StudentsCMSSP\app\__init__.py:862]
2025-06-21 17:27:29,053 INFO: 批量生成明细账请求: user_area=44, request_data={'year': 2025, 'month': 6, 'subject_ids': []} [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:132]
2025-06-21 17:27:29,053 INFO: 获取有发生额的科目: area_id=44, year=2025, month=6 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:153]
2025-06-21 17:27:29,069 INFO: 找到有发生额的科目数量: 3 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:155]
2025-06-21 17:27:29,069 INFO: 生成明细账: subject_id=206 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:166]
2025-06-21 17:27:29,088 INFO: 生成明细账: subject_id=208 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:166]
2025-06-21 17:27:29,194 INFO: 生成明细账: subject_id=220 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:166]
2025-06-21 17:27:29,245 INFO: 批量生成明细账完成: {'success': True, 'message': '批量生成完成，成功 3 个科目', 'results': [{'subject_id': 206, 'result': {'success': True, 'message': '成功生成 1201-原材料 2025年6月明细账', 'records_count': 3, 'opening_balance': 0.0, 'closing_balance': 1948.4, 'save_result': {'saved': False, 'message': '明细账未持久化保存，仅实时生成'}}}, {'subject_id': 208, 'result': {'success': True, 'message': '成功生成 120102-肉类 2025年6月明细账', 'records_count': 3, 'opening_balance': 0.0, 'closing_balance': 8300.0, 'save_result': {'saved': False, 'message': '明细账未持久化保存，仅实时生成'}}}, {'subject_id': 220, 'result': {'success': True, 'message': '成功生成 2001-应付账款 2025年6月明细账', 'records_count': 3, 'opening_balance': 0.0, 'closing_balance': -10248.4, 'save_result': {'saved': False, 'message': '明细账未持久化保存，仅实时生成'}}}]} [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:186]
2025-06-21 17:29:45,598 ERROR: Exception on /financial/ledgers/general [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\ledgers.py", line 224, in general_ledger
    end_date_obj = date.today()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\financial\ledgers\general.html", line 1, in top-level template code
    {% extends "financial/base.html" %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 429, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 131, in block 'content'
    {{ self.financial_content() }}
  File "C:\StudentsCMSSP\app\templates\financial\ledgers\general.html", line 227, in block 'financial_content'
    <span class="uf-amount uf-debit-amount">{{ "{:,.2f}"|format(item.period_debit|float) }}</span>
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\filters.py", line 1016, in do_format
    return soft_str(value) % (kwargs or args)
TypeError: not all arguments converted during string formatting
2025-06-21 17:29:48,779 ERROR: Exception on /financial/ledgers/general [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\ledgers.py", line 224, in general_ledger
    end_date_obj = date.today()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\financial\ledgers\general.html", line 1, in top-level template code
    {% extends "financial/base.html" %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 429, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 131, in block 'content'
    {{ self.financial_content() }}
  File "C:\StudentsCMSSP\app\templates\financial\ledgers\general.html", line 227, in block 'financial_content'
    <span class="uf-amount uf-debit-amount">{{ "{:,.2f}"|format(item.period_debit|float) }}</span>
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\filters.py", line 1016, in do_format
    return soft_str(value) % (kwargs or args)
TypeError: not all arguments converted during string formatting
